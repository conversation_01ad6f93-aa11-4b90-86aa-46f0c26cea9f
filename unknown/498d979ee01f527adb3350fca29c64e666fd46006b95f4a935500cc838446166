import 'dart:ui';

import '../../colors.dart';

class CategoryColors {
  // 可用的颜色选项
  static const availableColors = [
    Color(0xFFEF4444), // Red
    Color(0xFFF97316), // Orange
    Color(0xFFEAB308), // Yellow
    Color(0xFF22C55E), // Green
    Color(0xFF06B6D4), // Cyan
    Color(0xFF3B82F6), // Blue
    Color(0xFF8B5CF6), // Purple
    Color(0xFFEC4899), // Pink
    Color(0xFF64748B), // Slate
    Color(0xFF374151), // Gray
    Color(0xFF991B1B), // Dark Red
    Color(0xFF92400E), // Dark Orange
    Color(0xFF365314), // Dark Green
    Color(0xFF0E7490), // Dark Cyan
    Color(0xFF1E40AF), // Dark Blue
    Color(0xFF6B21A8), // Dark Purple
  ];

  // 分类颜色映射（基于原型设计）
  static const Map<String, Color> colorMap = {
    '薪水': ErpColors.success,
    '餐飲': ErpColors.error,
    '購物': ErpColors.accent,
    '交通': ErpColors.primary,
    '娛樂': ErpColors.categoryEntertainment,
    '獎金': ErpColors.warning,
    '居家': ErpColors.categoryUtilities,
    '副業': ErpColors.accentDark,
    '健康': ErpColors.categoryHealth,
    '教育': ErpColors.categoryEducation,
    '水電': ErpColors.textSecondary,
    '其他': ErpColors.categoryOther,
  };

  // 分类背景颜色映射（浅色版本）
  static const Map<String, Color> backgroundColorMap = {
    '薪水': ErpColors.iconBackgroundGreen,
    '餐飲': ErpColors.iconBackgroundRed,
    '購物': ErpColors.iconBackgroundPurple,
    '交通': ErpColors.iconBackgroundBlue,
    '娛樂': ErpColors.iconBackgroundYellow,
    '獎金': ErpColors.iconBackgroundYellow,
    '居家': ErpColors.iconBackgroundYellow,
    '副業': ErpColors.iconBackgroundPurple,
    '健康': ErpColors.iconBackgroundBlue,
    '教育': ErpColors.iconBackgroundPurple,
    '水電': ErpColors.iconBackgroundYellow,
    '其他': ErpColors.iconBackgroundGreen,
  };

  /// 根据分类名称获取背景颜色
  static Color getBackgroundColor(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) {
      return backgroundColorMap['其他'] ?? ErpColors.iconBackgroundGreen;
    }
    return backgroundColorMap[categoryName] ?? ErpColors.iconBackgroundGreen;
  }

  /// 根据分类名称获取颜色
  static Color getColor(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) {
      return colorMap['其他'] ?? ErpColors.categoryOther;
    }
    return colorMap[categoryName] ?? ErpColors.categoryOther;
  }

  /// 根据颜色字符串获取颜色对象
  static Color parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return ErpColors.categoryOther;
    }

    // 如果是十六进制颜色字符串
    if (colorString.startsWith('#')) {
      try {
        return Color(
            int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      } catch (e) {
        return ErpColors.categoryOther;
      }
    }

    // 如果是预定义的颜色名称
    return getColor(colorString);
  }
}
