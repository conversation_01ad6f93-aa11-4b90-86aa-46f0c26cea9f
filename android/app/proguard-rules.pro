# Flutter 相關規則
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class io.flutter.plugin.editing.** { *; }

# 如果您使用了 Kotlin，請添加以下規則
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }

# 針對您使用的特定第三方庫，可以添加更多規則
# 例如 Firebase
-keep class com.google.firebase.** { *; }

# ObjectBox 相關規則（如果您的項目中使用了）
-keep class io.objectbox.** { *; }
