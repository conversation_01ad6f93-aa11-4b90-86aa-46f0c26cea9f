# pocket_trac

這是一個 **Flutter 跨平台移動應用項目**，名為 **PocketTrac**。以下是詳細的項目分析：

## Getting Started

```bash
dart run build_runner build
```

## 項目概覽

這是一個使用 Flutter 框架開發的跨平台應用，支援 iOS、Android、Web、Windows、Linux 和 macOS 平台。

## 核心技術棧

### 主要框架和工具

- **Flutter 3.24.5** - 主要開發框架
- **Dart** - 程式語言
- **FVM** - Flutter 版本管理（透過 .fvmrc 配置）

### 狀態管理和架構

- **GetX** - 狀態管理、路由管理和依賴注入（從 main.dart 可見使用 `GetMaterialApp`）
- **MVC/MVVM** 架構模式（從 app 目錄結構推測）

## 主要功能模組

### 資料層

- **ObjectBox** - 本地資料庫存儲（.flutter-plugins 中的 `objectbox_flutter_libs`）
- **數據模型**：
  - `ErpCategory` - 類別管理，支援顏色配置
  - `ErpOrder` - 訂單管理，包含地理位置信息

### 核心服務

- **Firebase 整合**：
  - `firebase_analytics` - 使用者行為分析
  - `firebase_crashlytics` - 崩潰報告和監控
  - `firebase_core` - Firebase 核心服務

### 設備和系統整合

- **設備信息**：`device_info_plus` - 獲取設備詳細信息
- **應用信息**：`package_info_plus` - 應用版本和包信息
- **分享功能**：`share_plus` - 跨平台分享

## 項目架構

### 目錄結構分析

```
lib/
├── app/
│   ├── models/          # 數據模型層
│   ├── providers/       # 數據提供者和服務
│   ├── routes/          # 路由配置
│   └── translations/    # 國際化支援
├── colors.dart          # 主題色彩配置
└── main.dart           # 應用入口點
```

### 平台特定配置

- **Android**：build.gradle 配置了發布簽名
- **iOS**：CocoaPods 管理依賴，支援多種 iOS 框架
- **主題支援**：日間模式 和 夜間模式

## 特色功能

### 1. 多語言支援

- 使用 GetX 的國際化功能
- 支援設備本地語言，回退至英文（美國）

### 2. 主題和 UI

- 自定義色彩系統 (`ErpColors`)
- Material Design 3 支援
- 深色/淺色主題適配

### 3. 錯誤監控和日誌

- **Talker** - 開發階段日誌記錄
- **Firebase Crashlytics** - 生產環境崩潰監控
- 自定義 `CrashlyticsTalkerObserver` 整合

### 4. 數據持久化

- ObjectBox 高性能本地資料庫
- 支援複雜的數據關係和查詢

## 開發和部署

### 建置配置

- **Android**：支援發布簽名配置
- **iOS**：使用 CocoaPods 進行依賴管理
- **多平台**：統一的 Flutter 建置流程

### 版本管理

- 使用 FVM 確保團隊 Flutter 版本一致性
- Git 版本控制（配置完整的 .gitignore）

## 安全性考量

- Firebase 隱私權配置
- 各平台的隱私權清單（PrivacyInfo.xcprivacy）
- 發布簽名保護
