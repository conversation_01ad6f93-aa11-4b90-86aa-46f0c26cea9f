import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pocket_trac/colors.dart';

void main() {
  group('Theme Configuration Tests', () {
    test('should provide light theme configuration', () {
      final lightTheme = ErpColors.lightTheme;
      
      expect(lightTheme.brightness, Brightness.light);
      expect(lightTheme.primaryColor, ErpColors.primary);
      expect(lightTheme.colorScheme.primary, ErpColors.primary);
      expect(lightTheme.colorScheme.secondary, ErpColors.accent);
      expect(lightTheme.scaffoldBackgroundColor, ErpColors.background);
    });

    test('should provide dark theme configuration', () {
      final darkTheme = ErpColors.darkTheme;
      
      expect(darkTheme.brightness, Brightness.dark);
      expect(darkTheme.primaryColor, ErpColors.primary);
      expect(darkTheme.colorScheme.primary, ErpColors.primary);
      expect(darkTheme.colorScheme.secondary, ErpColors.accent);
      expect(darkTheme.scaffoldBackgroundColor, ErpColors.darkBackground);
    });

    test('should have different background colors for light and dark themes', () {
      final lightTheme = ErpColors.lightTheme;
      final darkTheme = ErpColors.darkTheme;
      
      expect(lightTheme.scaffoldBackgroundColor, isNot(equals(darkTheme.scaffoldBackgroundColor)));
      expect(lightTheme.cardColor, isNot(equals(darkTheme.cardColor)));
    });

    test('should have consistent primary colors across themes', () {
      final lightTheme = ErpColors.lightTheme;
      final darkTheme = ErpColors.darkTheme;
      
      expect(lightTheme.primaryColor, equals(darkTheme.primaryColor));
      expect(lightTheme.colorScheme.primary, equals(darkTheme.colorScheme.primary));
      expect(lightTheme.colorScheme.secondary, equals(darkTheme.colorScheme.secondary));
    });

    test('should provide proper color constants', () {
      // Test primary colors
      expect(ErpColors.primary, const Color(0xFF3B82F6));
      expect(ErpColors.primaryLight, const Color(0xFF60A5FA));
      expect(ErpColors.primaryDark, const Color(0xFF1D4ED8));
      
      // Test accent colors
      expect(ErpColors.accent, const Color(0xFF8B5CF6));
      
      // Test background colors
      expect(ErpColors.background, const Color(0xFFFFFFFF));
      expect(ErpColors.darkBackground, const Color(0xFF121212));
      
      // Test text colors
      expect(ErpColors.textPrimary, const Color(0xFF1F2937));
      expect(ErpColors.darkTextPrimary, const Color(0xFFE0E0E0));
    });

    test('should provide gradient colors', () {
      expect(ErpColors.gradientStart, const Color(0xFF667EEA));
      expect(ErpColors.gradientEnd, const Color(0xFF764BA2));
      expect(ErpColors.darkGradientStart, const Color(0xFF2D3748));
      expect(ErpColors.darkGradientEnd, const Color(0xFF4A5568));
    });

    test('should provide status colors', () {
      expect(ErpColors.success, const Color(0xFF10B981));
      expect(ErpColors.warning, const Color(0xFFF59E0B));
      expect(ErpColors.error, const Color(0xFFEF4444));
      expect(ErpColors.info, const Color(0xFF3B82F6));
    });

    test('should provide income/expense colors', () {
      expect(ErpColors.income, const Color(0xFF10B981));
      expect(ErpColors.expense, const Color(0xFFEF4444));
      expect(ErpColors.balance, const Color(0xFFF59E0B));
    });

    test('should provide category colors', () {
      expect(ErpColors.categoryFood, const Color(0xFF3B82F6));
      expect(ErpColors.categoryTransport, const Color(0xFF8B5CF6));
      expect(ErpColors.categoryShopping, const Color(0xFFEC4899));
      expect(ErpColors.categoryOther, const Color(0xFF10B981));
    });
  });
}
