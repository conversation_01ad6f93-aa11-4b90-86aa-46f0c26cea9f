import 'dart:async';

import 'package:flutter_test/flutter_test.dart';
import 'package:objectid/objectid.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'package:pocket_trac/app/models/erp_order.dart';
import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';
import 'package:pocket_trac/objectbox.g.dart';

/// Test-specific BoxProvider that uses in-memory ObjectBox store
class TestBoxProvider extends BoxProvider {
  Store? _testStore;

  TestBoxProvider({required super.talker});

  @override
  Store get store => _testStore!;

  @override
  Future<void> init() async {
    if (_testStore == null || store.isClosed()) {
      _testStore = Store(getObjectBoxModel());
    }
  }

  @override
  void close() {
    _testStore?.close();
    _testStore = null;
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('OrderRepository Unit Tests', () {
    late OrderRepository orderRepository;
    late TestBoxProvider boxProvider;
    late Talker talker;

    setUp(() async {
      talker = TalkerFlutter.init();
      boxProvider = TestBoxProvider(talker: talker);
      await boxProvider.init();
      orderRepository = OrderRepository(boxProvider);
      
      // Clear any existing data to ensure test isolation
      await orderRepository.deleteAllAsync();
    });

    tearDown(() async {
      try {
        // Clean up all data after each test
        await orderRepository.deleteAllAsync();
        boxProvider.close();
      } catch (e) {
        // Ignore close errors in tests
      }
    });

    group('Constructor and Basic Properties', () {
      test('should create OrderRepository with BoxProvider', () {
        // Assert
        expect(orderRepository.boxProvider, equals(boxProvider));
        expect(orderRepository.talker, equals(talker));
      });

      test('should have access to talker through boxProvider', () {
        // Assert
        expect(orderRepository.talker, isA<Talker>());
        expect(orderRepository.talker, equals(boxProvider.talker));
      });
    });

    group('ErpOrder Model Tests', () {
      test('should create ErpOrder with correct properties', () {
        // Arrange & Act
        final order = ErpOrder(
          type: 1,
          amount: 100.50,
          note: '測試訂單',
          latitude: 25.0330,
          longitude: 121.5654,
        );

        // Assert
        expect(order.type, equals(1));
        expect(order.amount, equals(100.50));
        expect(order.note, equals('測試訂單'));
        expect(order.latitude, equals(25.0330));
        expect(order.longitude, equals(121.5654));
        expect(order.id, isNull);
        expect(order.createdAt, isNull);
        expect(order.updatedAt, isNull);
        expect(order.deletedAt, isNull);
        expect(order.objectId, isNull);
      });

      test('should copy order with new values', () {
        // Arrange
        final original = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '原始訂單',
        );

        // Act
        final copied = original.copyWith(
          amount: 200.0,
          note: '新訂單',
        );

        // Assert
        expect(copied.amount, equals(200.0));
        expect(copied.note, equals('新訂單'));
        expect(copied.type, equals(1)); // Should keep original value
        expect(copied.id, equals(original.id));
      });

      test('should convert to and from JSON correctly', () {
        // Arrange
        final now = DateTime.now();
        final triggerAt = DateTime.now().add(Duration(days: 1));
        final order = ErpOrder(
          id: 1,
          type: 1,
          amount: 100.50,
          note: '測試訂單',
          latitude: 25.0330,
          longitude: 121.5654,
          createdAt: now,
          updatedAt: now,
          triggerAt: triggerAt,
          objectId: ObjectId().hexString,
          parentTable: 'categories',
          parentObjectId: 'category123',
        );

        // Act
        final json = order.toJson();
        final fromJson = ErpOrder.fromJson(json);

        // Assert
        expect(fromJson.id, equals(order.id));
        expect(fromJson.type, equals(order.type));
        expect(fromJson.amount, equals(order.amount));
        expect(fromJson.note, equals(order.note));
        expect(fromJson.latitude, equals(order.latitude));
        expect(fromJson.longitude, equals(order.longitude));
        expect(fromJson.objectId, equals(order.objectId));
        expect(fromJson.parentTable, equals(order.parentTable));
        expect(fromJson.parentObjectId, equals(order.parentObjectId));
        expect(fromJson.createdAt?.millisecondsSinceEpoch,
               equals(order.createdAt?.millisecondsSinceEpoch));
        expect(fromJson.updatedAt?.millisecondsSinceEpoch,
               equals(order.updatedAt?.millisecondsSinceEpoch));
        expect(fromJson.triggerAt?.millisecondsSinceEpoch,
               equals(order.triggerAt?.millisecondsSinceEpoch));
      });

      test('should handle null values in JSON conversion', () {
        // Arrange
        final order = ErpOrder(amount: 50.0);

        // Act
        final json = order.toJson();
        final fromJson = ErpOrder.fromJson(json);

        // Assert
        expect(fromJson.amount, equals(50.0));
        expect(fromJson.id, isNull);
        expect(fromJson.type, isNull);
        expect(fromJson.note, isNull);
        expect(fromJson.latitude, isNull);
        expect(fromJson.longitude, isNull);
        expect(fromJson.createdAt, isNull);
        expect(fromJson.updatedAt, isNull);
        expect(fromJson.deletedAt, isNull);
        expect(fromJson.triggerAt, isNull);
        expect(fromJson.objectId, isNull);
        expect(fromJson.parentTable, isNull);
        expect(fromJson.parentObjectId, isNull);
      });
    });

    group('Basic CRUD Operations', () {
      test('should add order async', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '測試新增訂單',
        );

        // Act
        final id = await orderRepository.addAsync(order);

        // Assert
        expect(id, isA<int>());
        expect(id, greaterThan(0));
        expect(order.id, equals(id));
        expect(order.createdAt, isNotNull);
        expect(order.updatedAt, isNotNull);
        expect(order.objectId, isNotNull);
        expect(order.objectId!.length, equals(24));
      });

      test('should get order by id async', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '測試取得訂單',
        );
        final id = await orderRepository.addAsync(order);

        // Act
        final retrievedOrder = await orderRepository.getByIdAsync(id);

        // Assert
        expect(retrievedOrder, isNotNull);
        expect(retrievedOrder!.id, equals(id));
        expect(retrievedOrder.amount, equals(100.0));
        expect(retrievedOrder.note, equals('測試取得訂單'));
      });

      test('should get order by objectId async', () async {
        // Arrange
        final objectId = ObjectId().hexString;
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '測試取得訂單',
          objectId: objectId,
        );
        await orderRepository.addAsync(order);

        // Act
        final retrievedOrder = await orderRepository.getByObjectIdAsync(objectId);

        // Assert
        expect(retrievedOrder, isNotNull);
        expect(retrievedOrder!.objectId, equals(objectId));
        expect(retrievedOrder.amount, equals(100.0));
        expect(retrievedOrder.note, equals('測試取得訂單'));
      });

      test('should update order async', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '原始訂單',
        );
        final id = await orderRepository.addAsync(order);
        final originalUpdatedAt = order.updatedAt;

        // Wait a bit to ensure different timestamp
        await Future.delayed(Duration(milliseconds: 10));

        // Act
        order.amount = 200.0;
        order.note = '更新後的訂單';
        await orderRepository.updateAsync(order);

        // Assert
        final updatedOrder = await orderRepository.getByIdAsync(id);
        expect(updatedOrder!.amount, equals(200.0));
        expect(updatedOrder.note, equals('更新後的訂單'));
        expect(updatedOrder.updatedAt!.isAfter(originalUpdatedAt!), isTrue);
      });

      test('should save order async (add new)', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '測試保存新訂單',
        );

        // Act
        final id = await orderRepository.saveAsync(order);

        // Assert
        expect(id, isA<int>());
        expect(id, greaterThan(0));
        expect(order.id, equals(id));
        expect(order.createdAt, isNotNull);
        expect(order.updatedAt, isNotNull);
        expect(order.objectId, isNotNull);
      });

      test('should save order async (update existing)', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '原始訂單',
        );
        final id = await orderRepository.addAsync(order);
        final originalUpdatedAt = order.updatedAt;

        // Wait a bit to ensure different timestamp
        await Future.delayed(Duration(milliseconds: 10));

        // Act
        order.amount = 200.0;
        order.note = '更新後的訂單';
        final savedId = await orderRepository.saveAsync(order);

        // Assert
        expect(savedId, equals(id));
        final updatedOrder = await orderRepository.getByIdAsync(id);
        expect(updatedOrder!.amount, equals(200.0));
        expect(updatedOrder.note, equals('更新後的訂單'));
        expect(updatedOrder.updatedAt!.isAfter(originalUpdatedAt!), isTrue);
      });
    });

    group('Filter-based Query Tests', () {
      test('should get orders with basic filter', () async {
        // Arrange - Create multiple orders
        final orders = List.generate(15, (index) => ErpOrder(
          type: index % 2 == 0 ? 1 : 2,
          amount: (index + 1) * 10.0,
          note: '測試訂單 ${index + 1}',
        ));

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Get orders with pagination filter
        final filter = OrderFilter(
          limit: 5,
          offset: 0,
        );
        final firstPage = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(firstPage.length, equals(5));
        expect(firstPage.first.note, contains('測試訂單'));

        // Act - Get second page
        final secondPageFilter = OrderFilter(
          limit: 5,
          offset: 5,
        );
        final secondPage = await orderRepository.getOrdersAsync(secondPageFilter);

        // Assert
        expect(secondPage.length, equals(5));
      });

      test('should get orders by type filter', () async {
        // Arrange
        final orders = [
          ErpOrder(type: 1, amount: 100.0, note: '收入1'),
          ErpOrder(type: 1, amount: 200.0, note: '收入2'),
          ErpOrder(type: 2, amount: 50.0, note: '支出1'),
          ErpOrder(type: 2, amount: 75.0, note: '支出2'),
        ];

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Get type 1 orders
        final filter = OrderFilter(
          type: 1,
          limit: 10,
          offset: 0,
        );
        final type1Orders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(type1Orders.length, equals(2));
        expect(type1Orders.every((order) => order.type == 1), isTrue);
      });

      test('should get orders by date range filter', () async {
        // Arrange
        final now = DateTime.now();
        final yesterday = now.subtract(Duration(days: 1));
        final tomorrow = now.add(Duration(days: 1));

        final orders = [
          ErpOrder(type: 1, amount: 100.0, note: '昨天', triggerAt: yesterday),
          ErpOrder(type: 1, amount: 200.0, note: '今天', triggerAt: now),
          ErpOrder(type: 1, amount: 300.0, note: '明天', triggerAt: tomorrow),
        ];

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Get orders for today
        final filter = OrderFilter(
          startDate: now.subtract(Duration(hours: 1)),
          endDate: now.add(Duration(hours: 1)),
          limit: 10,
          offset: 0,
        );
        final todayOrders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(todayOrders.length, equals(1));
        expect(todayOrders.first.note, equals('今天'));
      });

      test('should get orders by amount range filter', () async {
        // Arrange
        final orders = [
          ErpOrder(type: 1, amount: 50.0, note: '小額'),
          ErpOrder(type: 1, amount: 150.0, note: '中額'),
          ErpOrder(type: 1, amount: 250.0, note: '大額'),
        ];

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Get orders in amount range
        final filter = OrderFilter(
          minAmount: 100.0,
          maxAmount: 200.0,
          limit: 10,
          offset: 0,
        );
        final midRangeOrders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(midRangeOrders.length, equals(1));
        expect(midRangeOrders.first.amount, equals(150.0));
        expect(midRangeOrders.first.note, equals('中額'));
      });

      test('should get orders by note search filter', () async {
        // Arrange
        final orders = [
          ErpOrder(type: 1, amount: 100.0, note: '咖啡店消費'),
          ErpOrder(type: 1, amount: 200.0, note: '餐廳用餐'),
          ErpOrder(type: 1, amount: 300.0, note: '咖啡豆購買'),
        ];

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Search orders by note
        final filter = OrderFilter(
          noteSearch: '咖啡',
          limit: 10,
          offset: 0,
        );
        final coffeeOrders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(coffeeOrders.length, equals(2));
        expect(coffeeOrders.every((order) => order.note!.contains('咖啡')), isTrue);
      });

      test('should get orders with combined filters', () async {
        // Arrange
        final now = DateTime.now();
        final orders = [
          ErpOrder(type: 1, amount: 100.0, note: '咖啡店消費', triggerAt: now),
          ErpOrder(type: 2, amount: 150.0, note: '咖啡豆購買', triggerAt: now),
          ErpOrder(type: 1, amount: 200.0, note: '餐廳用餐', triggerAt: now),
        ];

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Get orders with multiple filters
        final filter = OrderFilter(
          type: 1,
          noteSearch: '咖啡',
          minAmount: 50.0,
          maxAmount: 120.0,
          startDate: now.subtract(Duration(hours: 1)),
          endDate: now.add(Duration(hours: 1)),
          limit: 10,
          offset: 0,
        );
        final filteredOrders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(filteredOrders.length, equals(1));
        expect(filteredOrders.first.type, equals(1));
        expect(filteredOrders.first.note, contains('咖啡'));
        expect(filteredOrders.first.amount, equals(100.0));
      });

      test('should get orders by categoryId filter', () async {
        // Arrange
        final category1 = ErpCategory(
          name: '餐飲',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final category2 = ErpCategory(
          name: '交通',
          color: '#00FF00',
          icon: 'transport',
        );

        // Save categories to get IDs
        final categoryBox = boxProvider.store.box<ErpCategory>();
        final category1Id = await categoryBox.putAsync(category1);
        final category2Id = await categoryBox.putAsync(category2);
        category1.id = category1Id;
        category2.id = category2Id;

        final orders = [
          ErpOrder(type: 1, amount: 100.0, note: '餐廳用餐'),
          ErpOrder(type: 1, amount: 200.0, note: '咖啡店'),
          ErpOrder(type: 1, amount: 50.0, note: '公車費'),
        ];

        // Set category relationships
        orders[0].parent.target = category1;
        orders[1].parent.target = category1;
        orders[2].parent.target = category2;

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Get orders for category1 (餐飲)
        final filter = OrderFilter(
          categoryId: category1Id,
          limit: 10,
          offset: 0,
        );
        final category1Orders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(category1Orders.length, equals(2));
        expect(category1Orders.every((order) => order.parent.target?.id == category1Id), isTrue);
        expect(category1Orders.map((o) => o.note).toList(), containsAll(['餐廳用餐', '咖啡店']));
      });

      test('should get orders excluding deleted by default', () async {
        // Arrange
        final order1 = ErpOrder(type: 1, amount: 100.0, note: '正常訂單');
        final order2 = ErpOrder(type: 1, amount: 200.0, note: '要刪除的訂單');

        final id1 = await orderRepository.addAsync(order1);
        final id2 = await orderRepository.addAsync(order2);
        await orderRepository.softDeleteAsync(id2);

        // Act - Get orders with default filter (excludes deleted)
        final filter = OrderFilter(limit: 10, offset: 0);
        final activeOrders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(activeOrders.length, equals(1));
        expect(activeOrders.first.id, equals(id1));
        expect(activeOrders.first.note, equals('正常訂單'));
      });

      test('should get orders including deleted when specified', () async {
        // Arrange
        final order1 = ErpOrder(type: 1, amount: 100.0, note: '正常訂單');
        final order2 = ErpOrder(type: 1, amount: 200.0, note: '已刪除的訂單');

        await orderRepository.addAsync(order1);
        final id2 = await orderRepository.addAsync(order2);
        await orderRepository.softDeleteAsync(id2);

        // Act - Get orders including deleted
        final filter = OrderFilter(
          includeDeleted: true,
          limit: 10,
          offset: 0,
        );
        final allOrders = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(allOrders.length, equals(2));
      });

      test('should handle empty filter results', () async {
        // Act - Try to get orders with filter when none exist
        final filter = OrderFilter(limit: 10, offset: 0);
        final emptyResult = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(emptyResult, isEmpty);
      });

      test('should handle filter with large offset', () async {
        // Arrange - Create only 5 orders
        final orders = List.generate(5, (index) => ErpOrder(
          type: 1,
          amount: (index + 1) * 10.0,
          note: '測試訂單 ${index + 1}',
        ));

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act - Try to get orders with offset larger than total count
        final filter = OrderFilter(limit: 10, offset: 100);
        final result = await orderRepository.getOrdersAsync(filter);

        // Assert
        expect(result, isEmpty);
      });
    });

    group('Soft Delete Operations', () {
      test('should soft delete order async', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '要刪除的訂單',
        );
        final id = await orderRepository.addAsync(order);

        // Act
        final result = await orderRepository.softDeleteAsync(id);

        // Assert
        expect(result, isTrue);
        final deletedOrder = await orderRepository.getByIdAsync(id);
        expect(deletedOrder!.deletedAt, isNotNull);
      });

      test('should restore soft deleted order async', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '要恢復的訂單',
        );
        final id = await orderRepository.addAsync(order);
        await orderRepository.softDeleteAsync(id);

        // Act
        final result = await orderRepository.restoreAsync(id);

        // Assert
        expect(result, isTrue);
        final restoredOrder = await orderRepository.getByIdAsync(id);
        expect(restoredOrder!.deletedAt, isNull);
      });

      test('should get all orders excluding deleted by default', () async {
        // Arrange
        final order1 = ErpOrder(type: 1, amount: 100.0, note: '正常訂單');
        final order2 = ErpOrder(type: 1, amount: 200.0, note: '要刪除的訂單');

        final id1 = await orderRepository.addAsync(order1);
        final id2 = await orderRepository.addAsync(order2);
        await orderRepository.softDeleteAsync(id2);

        // Act
        final activeOrders = await orderRepository.getAllAsync();

        // Assert
        expect(activeOrders.length, equals(1));
        expect(activeOrders.first.id, equals(id1));
        expect(activeOrders.first.note, equals('正常訂單'));
      });

      test('should get all orders including deleted when specified', () async {
        // Arrange
        final order1 = ErpOrder(type: 1, amount: 100.0, note: '正常訂單');
        final order2 = ErpOrder(type: 1, amount: 200.0, note: '已刪除的訂單');

        await orderRepository.addAsync(order1);
        final id2 = await orderRepository.addAsync(order2);
        await orderRepository.softDeleteAsync(id2);

        // Act
        final allOrders = await orderRepository.getAllAsync(includeDeleted: true);

        // Assert
        expect(allOrders.length, equals(2));
      });

      test('should hard delete order async', () async {
        // Arrange
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '要永久刪除的訂單',
        );
        final id = await orderRepository.addAsync(order);

        // Act
        final result = await orderRepository.hardDeleteAsync(id);

        // Assert
        expect(result, isTrue);
        final deletedOrder = await orderRepository.getByIdAsync(id);
        expect(deletedOrder, isNull);
      });
    });

    group('Count Operations', () {
      test('should count orders async', () async {
        // Arrange
        final orders = List.generate(5, (index) => ErpOrder(
          type: 1,
          amount: (index + 1) * 10.0,
          note: '測試訂單 ${index + 1}',
        ));

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act
        final count = await orderRepository.countAsync();

        // Assert
        expect(count, equals(5));
      });

      test('should count orders excluding deleted by default', () async {
        // Arrange
        final order1 = ErpOrder(type: 1, amount: 100.0, note: '正常訂單');
        final order2 = ErpOrder(type: 1, amount: 200.0, note: '要刪除的訂單');

        await orderRepository.addAsync(order1);
        final id2 = await orderRepository.addAsync(order2);
        await orderRepository.softDeleteAsync(id2);

        // Act
        final count = await orderRepository.countAsync();

        // Assert
        expect(count, equals(1));
      });

      test('should count orders including deleted when specified', () async {
        // Arrange
        final order1 = ErpOrder(type: 1, amount: 100.0, note: '正常訂單');
        final order2 = ErpOrder(type: 1, amount: 200.0, note: '已刪除的訂單');

        await orderRepository.addAsync(order1);
        final id2 = await orderRepository.addAsync(order2);
        await orderRepository.softDeleteAsync(id2);

        // Act
        final count = await orderRepository.countAsync(includeDeleted: true);

        // Assert
        expect(count, equals(2));
      });
    });

    group('Batch Operations', () {
      test('should put many orders async', () async {
        // Arrange
        final orders = List.generate(3, (index) => ErpOrder(
          type: 1,
          amount: (index + 1) * 100.0,
          note: '批量訂單 ${index + 1}',
        ));

        // Act
        final ids = await orderRepository.putManyAsync(orders);

        // Assert
        expect(ids.length, equals(3));
        expect(ids.every((id) => id > 0), isTrue);

        // Verify all orders have proper timestamps and objectIds
        for (final order in orders) {
          expect(order.createdAt, isNotNull);
          expect(order.updatedAt, isNotNull);
          expect(order.objectId, isNotNull);
          expect(order.objectId!.length, equals(24));
        }
      });

      test('should delete all orders async', () async {
        // Arrange
        final orders = List.generate(5, (index) => ErpOrder(
          type: 1,
          amount: (index + 1) * 10.0,
          note: '要刪除的訂單 ${index + 1}',
        ));

        for (final order in orders) {
          await orderRepository.addAsync(order);
        }

        // Act
        final deletedCount = await orderRepository.deleteAllAsync();

        // Assert
        expect(deletedCount, equals(5));
        final remainingOrders = await orderRepository.getAllAsync();
        expect(remainingOrders, isEmpty);
      });
    });

    group('OrderFilter Tests', () {
      test('should create OrderFilter with default values', () {
        // Act
        final filter = OrderFilter();

        // Assert
        expect(filter.includeDeleted, isFalse);
        expect(filter.limit, equals(20));
        expect(filter.offset, equals(0));
        expect(filter.ascending, isTrue);
        expect(filter.type, isNull);
        expect(filter.minAmount, isNull);
        expect(filter.maxAmount, isNull);
        expect(filter.startDate, isNull);
        expect(filter.endDate, isNull);
        expect(filter.noteSearch, isNull);
        expect(filter.sortBy, isNull);
        expect(filter.categoryId, isNull);
      });

      test('should create OrderFilter with custom values', () {
        // Arrange
        final now = DateTime.now();

        // Act
        final filter = OrderFilter(
          type: 1,
          minAmount: 100.0,
          maxAmount: 500.0,
          startDate: now,
          endDate: now.add(Duration(days: 1)),
          noteSearch: '咖啡',
          categoryId: 123,
          includeDeleted: true,
          limit: 10,
          offset: 5,
          sortBy: 'amount',
          ascending: false,
        );

        // Assert
        expect(filter.type, equals(1));
        expect(filter.minAmount, equals(100.0));
        expect(filter.maxAmount, equals(500.0));
        expect(filter.startDate, equals(now));
        expect(filter.endDate, equals(now.add(Duration(days: 1))));
        expect(filter.noteSearch, equals('咖啡'));
        expect(filter.categoryId, equals(123));
        expect(filter.includeDeleted, isTrue);
        expect(filter.limit, equals(10));
        expect(filter.offset, equals(5));
        expect(filter.sortBy, equals('amount'));
        expect(filter.ascending, isFalse);
      });

      test('should copy OrderFilter with new values', () {
        // Arrange
        final original = OrderFilter(
          type: 1,
          limit: 10,
          offset: 0,
        );

        // Act
        final copied = original.copyWith(
          type: 2,
          limit: 20,
          noteSearch: '測試',
          categoryId: 456,
        );

        // Assert
        expect(copied.type, equals(2));
        expect(copied.limit, equals(20));
        expect(copied.noteSearch, equals('測試'));
        expect(copied.categoryId, equals(456));
        expect(copied.offset, equals(0)); // Should keep original value
      });
    });

    group('Category Relationship Tests', () {
      test('should set and get category relationship', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試分類',
          color: '#FF0000',
          icon: 'test_icon',
        );

        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '有分類的訂單',
        );

        // Act
        order.parent.target = category;

        // Assert
        expect(order.parent.target, equals(category));
        expect(order.parent.target?.name, equals('測試分類'));
      });
    });

    group('Error Handling Tests', () {
      test('should return null when getting non-existent order by id', () async {
        // Act
        final nonExistentOrder = await orderRepository.getByIdAsync(99999);

        // Assert
        expect(nonExistentOrder, isNull);
      });

      test('should return null when getting non-existent order by objectId', () async {
        // Act
        final nonExistentOrder = await orderRepository.getByObjectIdAsync('nonexistent');

        // Assert
        expect(nonExistentOrder, isNull);
      });

      test('should return false when soft deleting non-existent order', () async {
        // Act
        final result = await orderRepository.softDeleteAsync(99999);

        // Assert
        expect(result, isFalse);
      });

      test('should return false when restoring non-existent order', () async {
        // Act
        final result = await orderRepository.restoreAsync(99999);

        // Assert
        expect(result, isFalse);
      });

      test('should return false when hard deleting non-existent order', () async {
        // Act
        final result = await orderRepository.hardDeleteAsync(99999);

        // Assert
        expect(result, isFalse);
      });
    });

    group('Stream Operations', () {
      test('should watch all orders stream', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedData = <List<ErpOrder>>[];
        late StreamSubscription subscription;

        // Act
        subscription = orderRepository.watchAll().listen((orders) {
          receivedData.add(orders);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial empty state
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Add an order
        final orderCompleter = Completer<void>();
        final order = ErpOrder(
          type: 1,
          amount: 100.0,
          note: '測試訂單',
        );
        
        // Listen for the next event
        bool orderAdded = false;
        subscription.onData((orders) {
          receivedData.add(orders);
          if (orders.isNotEmpty && !orderAdded) {
            orderAdded = true;
            if (!orderCompleter.isCompleted) {
              orderCompleter.complete();
            }
          }
        });
        
        await orderRepository.addAsync(order);
        await orderCompleter.future;

        expect(receivedData.last.length, equals(1));
        expect(receivedData.last.first.note, equals('測試訂單'));

        // Cleanup
        await subscription.cancel();
      });

      test('should watch active orders stream excluding deleted', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedData = <List<ErpOrder>>[];
        late StreamSubscription subscription;

        // Act
        subscription = orderRepository.watchActive().listen((orders) {
          receivedData.add(orders);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Add an order
        final orderAddedCompleter = Completer<void>();
        final order = ErpOrder(type: 1, amount: 100.0, note: '活躍訂單');
        
        bool orderAdded = false;
        subscription.onData((orders) {
          receivedData.add(orders);
          if (orders.isNotEmpty && !orderAdded) {
            orderAdded = true;
            if (!orderAddedCompleter.isCompleted) {
              orderAddedCompleter.complete();
            }
          }
        });
        
        final id = await orderRepository.addAsync(order);
        await orderAddedCompleter.future;
        expect(receivedData.last.length, equals(1));

        // Soft delete the order
        final orderDeletedCompleter = Completer<void>();
        bool orderDeleted = false;
        subscription.onData((orders) {
          receivedData.add(orders);
          if (orders.isEmpty && orderAdded && !orderDeleted) {
            orderDeleted = true;
            if (!orderDeletedCompleter.isCompleted) {
              orderDeletedCompleter.complete();
            }
          }
        });
        
        await orderRepository.softDeleteAsync(id);
        await orderDeletedCompleter.future;

        // Should not include deleted orders
        expect(receivedData.last, isEmpty);

        // Cleanup
        await subscription.cancel();
      });

      test('should watch orders with filter', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedData = <List<ErpOrder>>[];
        final filter = OrderFilter(type: 1, limit: 10, offset: 0);
        late StreamSubscription subscription;

        // Act
        subscription = orderRepository.watchOrdersWithFilter(filter).listen((orders) {
          receivedData.add(orders);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Add orders of different types
        final type1AddedCompleter = Completer<void>();
        bool type1Added = false;
        subscription.onData((orders) {
          receivedData.add(orders);
          if (orders.length == 1 && !type1Added) {
            type1Added = true;
            if (!type1AddedCompleter.isCompleted) {
              type1AddedCompleter.complete();
            }
          }
        });

        await orderRepository.addAsync(ErpOrder(type: 1, amount: 100.0, note: '類型1'));
        await type1AddedCompleter.future;
        expect(receivedData.last.length, equals(1));

        // Add type 2 order - should not appear in filtered results
        await orderRepository.addAsync(ErpOrder(type: 2, amount: 200.0, note: '類型2'));

        // Wait a bit for any potential updates
        await Future.delayed(Duration(milliseconds: 50));

        // Should still only have type 1 orders
        expect(receivedData.last.length, equals(1));
        expect(receivedData.last.first.type, equals(1));

        // Cleanup
        await subscription.cancel();
      });

      test('should watch orders with categoryId filter', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試分類',
          color: '#FF0000',
          icon: 'test_icon',
        );

        // Save category to get ID
        final categoryBox = boxProvider.store.box<ErpCategory>();
        final categoryId = await categoryBox.putAsync(category);
        category.id = categoryId;

        final completer = Completer<void>();
        final receivedData = <List<ErpOrder>>[];
        final filter = OrderFilter(categoryId: categoryId, limit: 10, offset: 0);
        late StreamSubscription subscription;

        // Act
        subscription = orderRepository.watchOrdersWithFilter(filter).listen((orders) {
          receivedData.add(orders);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Add order with category
        final categoryOrderCompleter = Completer<void>();
        bool categoryOrderAdded = false;
        subscription.onData((orders) {
          receivedData.add(orders);
          if (orders.length == 1 && !categoryOrderAdded) {
            categoryOrderAdded = true;
            if (!categoryOrderCompleter.isCompleted) {
              categoryOrderCompleter.complete();
            }
          }
        });

        final orderWithCategory = ErpOrder(type: 1, amount: 100.0, note: '有分類的訂單');
        orderWithCategory.parent.target = category;
        await orderRepository.addAsync(orderWithCategory);
        await categoryOrderCompleter.future;
        expect(receivedData.last.length, equals(1));
        expect(receivedData.last.first.parent.target?.id, equals(categoryId));

        // Add order without category - should not appear in filtered results
        await orderRepository.addAsync(ErpOrder(type: 1, amount: 200.0, note: '無分類的訂單'));

        // Wait a bit for any potential updates
        await Future.delayed(Duration(milliseconds: 50));

        // Should still only have orders with the specific category
        expect(receivedData.last.length, equals(1));
        expect(receivedData.last.first.parent.target?.id, equals(categoryId));

        // Cleanup
        await subscription.cancel();
      });

      test('should watch orders count stream', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedCounts = <int>[];
        late StreamSubscription subscription;

        // Act
        subscription = orderRepository.watchCount().listen((count) {
          receivedCounts.add(count);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial count
        await completer.future;
        expect(receivedCounts.first, equals(0));

        // Add orders
        final firstOrderCompleter = Completer<void>();
        bool firstOrderAdded = false;
        subscription.onData((count) {
          receivedCounts.add(count);
          if (count == 1 && !firstOrderAdded) {
            firstOrderAdded = true;
            if (!firstOrderCompleter.isCompleted) {
              firstOrderCompleter.complete();
            }
          }
        });
        
        await orderRepository.addAsync(ErpOrder(type: 1, amount: 100.0));
        await firstOrderCompleter.future;
        expect(receivedCounts.last, equals(1));

        final secondOrderCompleter = Completer<void>();
        bool secondOrderAdded = false;
        subscription.onData((count) {
          receivedCounts.add(count);
          if (count == 2 && firstOrderAdded && !secondOrderAdded) {
            secondOrderAdded = true;
            if (!secondOrderCompleter.isCompleted) {
              secondOrderCompleter.complete();
            }
          }
        });
        
        await orderRepository.addAsync(ErpOrder(type: 1, amount: 200.0));
        await secondOrderCompleter.future;
        expect(receivedCounts.last, equals(2));

        // Cleanup
        await subscription.cancel();
      });

      test('should watch specific order by id', () async {
        // Arrange
        final order = ErpOrder(type: 1, amount: 100.0, note: '要監聽的訂單');
        final id = await orderRepository.addAsync(order);

        final completer = Completer<void>();
        final receivedOrders = <ErpOrder?>[];
        late StreamSubscription subscription;

        // Act
        subscription = orderRepository.watchById(id).listen((order) {
          receivedOrders.add(order);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial order
        await completer.future;
        expect(receivedOrders.first, isNotNull);
        expect(receivedOrders.first!.note, equals('要監聽的訂單'));

        // Update the order
        final updateCompleter = Completer<void>();
        bool orderUpdated = false;
        subscription.onData((updatedOrder) {
          receivedOrders.add(updatedOrder);
          if (updatedOrder?.note == '更新後的訂單' && !orderUpdated) {
            orderUpdated = true;
            if (!updateCompleter.isCompleted) {
              updateCompleter.complete();
            }
          }
        });
        
        order.note = '更新後的訂單';
        await orderRepository.updateAsync(order);
        await updateCompleter.future;
        expect(receivedOrders.last!.note, equals('更新後的訂單'));

        // Cleanup
        await subscription.cancel();
      });
    });
  });
}
