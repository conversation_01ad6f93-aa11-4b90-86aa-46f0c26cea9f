import 'dart:async';

import 'package:flutter_test/flutter_test.dart';
import 'package:objectid/objectid.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/repositories/category_repository.dart';
import 'package:pocket_trac/objectbox.g.dart';

/// Test-specific BoxProvider that uses in-memory ObjectBox store
class TestBoxProvider extends BoxProvider {
  Store? _testStore;

  TestBoxProvider({required super.talker});

  @override
  Store get store => _testStore!;

  @override
  Future<void> init() async {
    if (_testStore == null || store.isClosed()) {
      _testStore = Store(getObjectBoxModel());
    }
  }

  @override
  void close() {
    _testStore?.close();
    _testStore = null;
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('CategoryRepository Unit Tests', () {
    late CategoryRepository categoryRepository;
    late TestBoxProvider boxProvider;
    late Talker talker;

    setUp(() async {
      talker = TalkerFlutter.init();
      boxProvider = TestBoxProvider(talker: talker);
      await boxProvider.init();
      categoryRepository = CategoryRepository(boxProvider);

      // Clear any existing data to ensure test isolation
      await categoryRepository.deleteAllAsync();
    });

    tearDown(() async {
      try {
        // Clean up all data after each test
        await categoryRepository.deleteAllAsync();
        boxProvider.close();
      } catch (e) {
        // Ignore close errors in tests
      }
    });

    group('Constructor and Basic Properties', () {
      test('should create CategoryRepository with BoxProvider', () {
        // Assert
        expect(categoryRepository.boxProvider, equals(boxProvider));
        expect(categoryRepository.talker, equals(talker));
      });

      test('should have access to talker through boxProvider', () {
        // Assert
        expect(categoryRepository.talker, isA<Talker>());
        expect(categoryRepository.talker, equals(boxProvider.talker));
      });
    });

    group('ErpCategory Model Tests', () {
      test('should create ErpCategory with all properties', () {
        // Arrange & Act
        final now = DateTime.now();
        final category = ErpCategory(
          id: 1,
          name: '測試分類',
          color: '#FF0000',
          icon: 'restaurant',
          objectId: ObjectId().hexString,
          createdAt: now,
          updatedAt: now,
        );

        // Assert
        expect(category.id, equals(1));
        expect(category.name, equals('測試分類'));
        expect(category.color, equals('#FF0000'));
        expect(category.icon, equals('restaurant'));
        expect(category.objectId, isNotNull);
        expect(category.objectId!.length, equals(24));
        expect(category.createdAt, equals(now));
        expect(category.updatedAt, equals(now));
        expect(category.deletedAt, isNull);
      });

      test('should copy ErpCategory with new values', () {
        // Arrange
        final original = ErpCategory(
          id: 1,
          name: '原始分類',
          color: '#FF0000',
          icon: 'restaurant',
        );

        // Act
        final copied = original.copyWith(
          name: '新分類',
          color: '#00FF00',
          icon: 'shopping',
        );

        // Assert
        expect(copied.name, equals('新分類'));
        expect(copied.color, equals('#00FF00'));
        expect(copied.icon, equals('shopping'));
        expect(copied.id, equals(original.id)); // Should keep original value
      });

      test('should convert ErpCategory to and from JSON', () {
        // Arrange
        final now = DateTime.now();
        final category = ErpCategory(
          id: 1,
          name: '測試分類',
          color: '#FF0000',
          icon: 'restaurant',
          objectId: ObjectId().hexString,
          createdAt: now,
          updatedAt: now,
        );

        // Act
        final json = category.toJson();
        final fromJson = ErpCategory.fromJson(json);

        // Assert
        expect(fromJson.id, equals(category.id));
        expect(fromJson.name, equals(category.name));
        expect(fromJson.color, equals(category.color));
        expect(fromJson.icon, equals(category.icon));
        expect(fromJson.objectId, equals(category.objectId));
        expect(fromJson.createdAt?.millisecondsSinceEpoch,
               equals(category.createdAt?.millisecondsSinceEpoch));
        expect(fromJson.updatedAt?.millisecondsSinceEpoch,
               equals(category.updatedAt?.millisecondsSinceEpoch));
      });

      test('should handle null values in JSON conversion', () {
        // Arrange
        final category = ErpCategory(name: '測試分類');

        // Act
        final json = category.toJson();
        final fromJson = ErpCategory.fromJson(json);

        // Assert
        expect(fromJson.name, equals(category.name));
        expect(fromJson.id, isNull);
        expect(fromJson.color, isNull);
        expect(fromJson.icon, isNull);
        expect(fromJson.objectId, isNull);
        expect(fromJson.createdAt, isNull);
        expect(fromJson.updatedAt, isNull);
        expect(fromJson.deletedAt, isNull);
      });
    });

    group('ObjectId Tests', () {
      test('should generate valid ObjectId', () {
        // Act
        final objectId = ObjectId().hexString;

        // Assert
        expect(objectId, isNotNull);
        expect(objectId.length, equals(24));
        expect(RegExp(r'^[0-9a-fA-F]{24}$').hasMatch(objectId), isTrue);
      });

      test('should generate unique ObjectIds', () {
        // Act
        final objectId1 = ObjectId().hexString;
        final objectId2 = ObjectId().hexString;

        // Assert
        expect(objectId1, isNot(equals(objectId2)));
      });
    });

    group('Basic CRUD Operations', () {
      test('should add category async', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試新增分類',
          color: '#FF0000',
          icon: 'restaurant',
        );

        // Act
        final id = await categoryRepository.addAsync(category);

        // Assert
        expect(id, isA<int>());
        expect(id, greaterThan(0));
        expect(category.id, equals(id));
        expect(category.createdAt, isNotNull);
        expect(category.updatedAt, isNotNull);
        expect(category.objectId, isNotNull);
        expect(category.objectId!.length, equals(24));
      });

      test('should update category async', () async {
        // Arrange
        final category = ErpCategory(
          name: '原始分類',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);

        // Wait a bit to ensure different timestamps
        await Future.delayed(const Duration(milliseconds: 10));

        final originalUpdatedAt = category.updatedAt;

        // Act
        category.name = '更新後的分類';
        category.color = '#00FF00';
        final updatedId = await categoryRepository.updateAsync(category);

        // Assert
        expect(updatedId, equals(id));
        expect(category.name, equals('更新後的分類'));
        expect(category.color, equals('#00FF00'));
        expect(category.updatedAt, isNotNull);
        expect(category.updatedAt!.isAfter(originalUpdatedAt!), isTrue);
      });

      test('should save category async (add new)', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試保存新分類',
          color: '#FF0000',
          icon: 'restaurant',
        );

        // Act
        final id = await categoryRepository.saveAsync(category);

        // Assert
        expect(id, isA<int>());
        expect(id, greaterThan(0));
        expect(category.id, equals(id));
        expect(category.createdAt, isNotNull);
        expect(category.updatedAt, isNotNull);
        expect(category.objectId, isNotNull);
      });

      test('should save category async (update existing)', () async {
        // Arrange
        final category = ErpCategory(
          name: '原始分類',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);

        // Wait a bit to ensure different timestamps
        await Future.delayed(const Duration(milliseconds: 10));

        final originalUpdatedAt = category.updatedAt;

        // Act
        category.name = '保存更新的分類';
        final savedId = await categoryRepository.saveAsync(category);

        // Assert
        expect(savedId, equals(id));
        expect(category.name, equals('保存更新的分類'));
        expect(category.updatedAt!.isAfter(originalUpdatedAt!), isTrue);
      });

      test('should get category by id async', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試獲取分類',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);

        // Act
        final retrievedCategory = await categoryRepository.getByIdAsync(id);

        // Assert
        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.id, equals(id));
        expect(retrievedCategory.name, equals('測試獲取分類'));
        expect(retrievedCategory.color, equals('#FF0000'));
        expect(retrievedCategory.icon, equals('restaurant'));
      });

      test('should return null for non-existent id', () async {
        // Act
        final retrievedCategory = await categoryRepository.getByIdAsync(99999);

        // Assert
        expect(retrievedCategory, isNull);
      });

      test('should get category by objectId async', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試ObjectId獲取',
          color: '#FF0000',
          icon: 'restaurant',
        );
        await categoryRepository.addAsync(category);

        // Act
        final retrievedCategory = await categoryRepository.getByObjectIdAsync(category.objectId!);

        // Assert
        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.objectId, equals(category.objectId));
        expect(retrievedCategory.name, equals('測試ObjectId獲取'));
      });

      test('should return null for non-existent objectId', () async {
        // Act
        final retrievedCategory = await categoryRepository.getByObjectIdAsync('nonexistent123456789012');

        // Assert
        expect(retrievedCategory, isNull);
      });
    });

    group('Search and Query Operations', () {
      test('should get all categories async (excluding deleted)', () async {
        // Arrange
        final categories = [
          ErpCategory(name: '餐飲', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '交通', color: '#00FF00', icon: 'transport'),
          ErpCategory(name: '購物', color: '#0000FF', icon: 'shopping'),
        ];

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Soft delete one category
        await categoryRepository.softDeleteAsync(categories[1].id!);

        // Act
        final allCategories = await categoryRepository.getAllAsync();

        // Assert
        expect(allCategories.length, equals(2)); // Should exclude deleted
        expect(allCategories.any((c) => c.name == '餐飲'), isTrue);
        expect(allCategories.any((c) => c.name == '購物'), isTrue);
        expect(allCategories.any((c) => c.name == '交通'), isFalse); // Deleted
      });

      test('should get all categories async (including deleted)', () async {
        // Arrange
        final categories = [
          ErpCategory(name: '餐飲', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '交通', color: '#00FF00', icon: 'transport'),
        ];

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Soft delete one category
        await categoryRepository.softDeleteAsync(categories[1].id!);

        // Act
        final allCategories = await categoryRepository.getAllAsync(includeDeleted: true);

        // Assert
        expect(allCategories.length, equals(2)); // Should include deleted
        expect(allCategories.any((c) => c.name == '餐飲'), isTrue);
        expect(allCategories.any((c) => c.name == '交通'), isTrue);
      });

      test('should search categories by name async', () async {
        // Arrange
        final categories = [
          ErpCategory(name: '餐飲美食', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '餐廳用餐', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '交通運輸', color: '#00FF00', icon: 'transport'),
          ErpCategory(name: '購物消費', color: '#0000FF', icon: 'shopping'),
        ];

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Act
        final searchResults = await categoryRepository.searchByNameAsync('餐');

        // Assert
        expect(searchResults.length, equals(2));
        expect(searchResults.every((c) => c.name!.contains('餐')), isTrue);
      });

      test('should return empty list for no search matches', () async {
        // Arrange
        final category = ErpCategory(name: '餐飲', color: '#FF0000', icon: 'restaurant');
        await categoryRepository.addAsync(category);

        // Act
        final searchResults = await categoryRepository.searchByNameAsync('不存在的分類');

        // Assert
        expect(searchResults, isEmpty);
      });

      test('should not return deleted categories in search', () async {
        // Arrange
        final categories = [
          ErpCategory(name: '餐飲美食', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '餐廳用餐', color: '#FF0000', icon: 'restaurant'),
        ];

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Soft delete one category
        await categoryRepository.softDeleteAsync(categories[1].id!);

        // Act
        final searchResults = await categoryRepository.searchByNameAsync('餐');

        // Assert
        expect(searchResults.length, equals(1));
        expect(searchResults.first.name, equals('餐飲美食'));
      });
    });

    group('Batch Operations', () {
      test('should put many categories async', () async {
        // Arrange
        final categories = [
          ErpCategory(name: '批量分類1', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '批量分類2', color: '#00FF00', icon: 'transport'),
          ErpCategory(name: '批量分類3', color: '#0000FF', icon: 'shopping'),
        ];

        // Act
        final ids = await categoryRepository.putManyAsync(categories);

        // Assert
        expect(ids.length, equals(3));
        expect(ids.every((id) => id > 0), isTrue);

        // Verify all categories have proper timestamps and objectIds
        for (final category in categories) {
          expect(category.createdAt, isNotNull);
          expect(category.updatedAt, isNotNull);
          expect(category.objectId, isNotNull);
          expect(category.objectId!.length, equals(24));
        }
      });

      test('should put many categories async (mixed new and existing)', () async {
        // Arrange
        final existingCategory = ErpCategory(name: '現有分類', color: '#FF0000', icon: 'restaurant');
        await categoryRepository.addAsync(existingCategory);

        final originalUpdatedAt = existingCategory.updatedAt;
        await Future.delayed(const Duration(milliseconds: 10));

        final newCategory = ErpCategory(name: '新分類', color: '#00FF00', icon: 'transport');
        existingCategory.name = '更新的現有分類';

        final categories = [existingCategory, newCategory];

        // Act
        final ids = await categoryRepository.putManyAsync(categories);

        // Assert
        expect(ids.length, equals(2));
        expect(ids.every((id) => id > 0), isTrue);

        // Check that existing category was updated
        expect(existingCategory.updatedAt!.isAfter(originalUpdatedAt!), isTrue);

        // Check that new category got timestamps
        expect(newCategory.createdAt, isNotNull);
        expect(newCategory.updatedAt, isNotNull);
        expect(newCategory.objectId, isNotNull);
      });
    });

    group('Delete Operations', () {
      test('should soft delete category async', () async {
        // Arrange
        final category = ErpCategory(
          name: '要軟刪除的分類',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);

        // Act
        final result = await categoryRepository.softDeleteAsync(id);

        // Assert
        expect(result, isTrue);

        // Verify category is soft deleted
        final deletedCategory = await categoryRepository.getByIdAsync(id);
        expect(deletedCategory, isNotNull);
        expect(deletedCategory!.deletedAt, isNotNull);

        // Verify it's not included in active categories
        final activeCategories = await categoryRepository.getAllAsync();
        expect(activeCategories.any((c) => c.id == id), isFalse);

        // Verify it's included when requesting deleted categories
        final allCategories = await categoryRepository.getAllAsync(includeDeleted: true);
        expect(allCategories.any((c) => c.id == id), isTrue);
      });

      test('should return false when soft deleting non-existent category', () async {
        // Act
        final result = await categoryRepository.softDeleteAsync(99999);

        // Assert
        expect(result, isFalse);
      });

      test('should restore soft deleted category async', () async {
        // Arrange
        final category = ErpCategory(
          name: '要恢復的分類',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);
        await categoryRepository.softDeleteAsync(id);

        // Act
        final result = await categoryRepository.restoreAsync(id);

        // Assert
        expect(result, isTrue);

        // Verify category is restored
        final restoredCategory = await categoryRepository.getByIdAsync(id);
        expect(restoredCategory, isNotNull);
        expect(restoredCategory!.deletedAt, isNull);

        // Verify it's included in active categories
        final activeCategories = await categoryRepository.getAllAsync();
        expect(activeCategories.any((c) => c.id == id), isTrue);
      });

      test('should return false when restoring non-deleted category', () async {
        // Arrange
        final category = ErpCategory(
          name: '正常分類',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);

        // Act
        final result = await categoryRepository.restoreAsync(id);

        // Assert
        expect(result, isFalse);
      });

      test('should return false when restoring non-existent category', () async {
        // Act
        final result = await categoryRepository.restoreAsync(99999);

        // Assert
        expect(result, isFalse);
      });

      test('should hard delete category async', () async {
        // Arrange
        final category = ErpCategory(
          name: '要永久刪除的分類',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);

        // Act
        final result = await categoryRepository.hardDeleteAsync(id);

        // Assert
        expect(result, isTrue);
        final deletedCategory = await categoryRepository.getByIdAsync(id);
        expect(deletedCategory, isNull);
      });

      test('should return false when hard deleting non-existent category', () async {
        // Act
        final result = await categoryRepository.hardDeleteAsync(99999);

        // Assert
        expect(result, isFalse);
      });

      test('should delete all categories async', () async {
        // Arrange
        final categories = List.generate(5, (index) => ErpCategory(
          name: '測試分類 ${index + 1}',
          color: '#FF0000',
          icon: 'restaurant',
        ));

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Act
        final deletedCount = await categoryRepository.deleteAllAsync();

        // Assert
        expect(deletedCount, equals(5));
        final remainingCategories = await categoryRepository.getAllAsync(includeDeleted: true);
        expect(remainingCategories, isEmpty);
      });
    });

    group('Count Operations', () {
      test('should count categories async (excluding deleted)', () async {
        // Arrange
        final categories = List.generate(5, (index) => ErpCategory(
          name: '測試分類 ${index + 1}',
          color: '#FF0000',
          icon: 'restaurant',
        ));

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Soft delete one category
        await categoryRepository.softDeleteAsync(categories[2].id!);

        // Act
        final count = await categoryRepository.countAsync();

        // Assert
        expect(count, equals(4)); // Should exclude deleted
      });

      test('should count categories async (including deleted)', () async {
        // Arrange
        final categories = List.generate(3, (index) => ErpCategory(
          name: '測試分類 ${index + 1}',
          color: '#FF0000',
          icon: 'restaurant',
        ));

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Soft delete one category
        await categoryRepository.softDeleteAsync(categories[1].id!);

        // Act
        final count = await categoryRepository.countAsync(includeDeleted: true);

        // Assert
        expect(count, equals(3)); // Should include deleted
      });

      test('should return zero count for empty repository', () async {
        // Act
        final count = await categoryRepository.countAsync();

        // Assert
        expect(count, equals(0));
      });
    });

    group('Stream Operations', () {
      test('should watch all categories stream', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedData = <List<ErpCategory>>[];
        late StreamSubscription subscription;

        // Act
        subscription = categoryRepository.watchAll().listen((categories) {
          receivedData.add(categories);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial empty state
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Add a category
        final categoryCompleter = Completer<void>();
        final category = ErpCategory(
          name: '測試分類',
          color: '#FF0000',
          icon: 'restaurant',
        );

        bool categoryAdded = false;
        subscription.onData((categories) {
          receivedData.add(categories);
          if (categories.isNotEmpty && !categoryAdded) {
            categoryAdded = true;
            if (!categoryCompleter.isCompleted) {
              categoryCompleter.complete();
            }
          }
        });

        await categoryRepository.addAsync(category);
        await categoryCompleter.future;

        // Assert
        expect(receivedData.last.length, equals(1));
        expect(receivedData.last.first.name, equals('測試分類'));

        // Clean up
        await subscription.cancel();
      });

      test('should watch active categories stream', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedData = <List<ErpCategory>>[];
        late StreamSubscription subscription;

        // Act
        subscription = categoryRepository.watchActive().listen((categories) {
          receivedData.add(categories);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Add a category
        final categoryAddedCompleter = Completer<void>();
        final category = ErpCategory(name: '活躍分類', color: '#FF0000', icon: 'restaurant');

        bool categoryAdded = false;
        subscription.onData((categories) {
          receivedData.add(categories);
          if (categories.isNotEmpty && !categoryAdded) {
            categoryAdded = true;
            if (!categoryAddedCompleter.isCompleted) {
              categoryAddedCompleter.complete();
            }
          }
        });

        await categoryRepository.addAsync(category);
        await categoryAddedCompleter.future;

        // Soft delete the category
        final categoryDeletedCompleter = Completer<void>();
        bool categoryDeleted = false;
        subscription.onData((categories) {
          receivedData.add(categories);
          if (categories.isEmpty && categoryAdded && !categoryDeleted) {
            categoryDeleted = true;
            if (!categoryDeletedCompleter.isCompleted) {
              categoryDeletedCompleter.complete();
            }
          }
        });

        await categoryRepository.softDeleteAsync(category.id!);
        await categoryDeletedCompleter.future;

        // Assert
        expect(receivedData.last, isEmpty); // Should not include deleted categories

        // Clean up
        await subscription.cancel();
      });

      test('should watch categories by name stream', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedData = <List<ErpCategory>>[];
        late StreamSubscription subscription;

        // Act
        subscription = categoryRepository.watchByName('餐').listen((categories) {
          receivedData.add(categories);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Add matching categories
        final categories = [
          ErpCategory(name: '餐飲', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '餐廳', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '交通', color: '#00FF00', icon: 'transport'), // Should not match
        ];

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Wait for updates
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(receivedData.last.length, equals(2)); // Only matching categories
        expect(receivedData.last.every((c) => c.name!.contains('餐')), isTrue);

        // Clean up
        await subscription.cancel();
      });

      test('should watch count stream', () async {
        // Arrange
        final completer = Completer<void>();
        final receivedData = <int>[];
        late StreamSubscription subscription;

        // Act
        subscription = categoryRepository.watchCount().listen((count) {
          receivedData.add(count);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, equals(0));

        // Add categories
        final categories = List.generate(3, (index) => ErpCategory(
          name: '測試分類 ${index + 1}',
          color: '#FF0000',
          icon: 'restaurant',
        ));

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        // Wait for updates
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(receivedData.last, equals(3));

        // Clean up
        await subscription.cancel();
      });

      test('should watch category by id stream', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試ID監聽',
          color: '#FF0000',
          icon: 'restaurant',
        );
        final id = await categoryRepository.addAsync(category);

        final completer = Completer<void>();
        final receivedData = <ErpCategory?>[];
        late StreamSubscription subscription;

        // Act
        subscription = categoryRepository.watchById(id).listen((cat) {
          receivedData.add(cat);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, isNotNull);
        expect(receivedData.first!.name, equals('測試ID監聽'));

        // Update the category
        category.name = '更新後的名稱';
        await categoryRepository.updateAsync(category);

        // Wait for updates
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(receivedData.last!.name, equals('更新後的名稱'));

        // Clean up
        await subscription.cancel();
      });

      test('should watch category by objectId stream', () async {
        // Arrange
        final category = ErpCategory(
          name: '測試ObjectId監聽',
          color: '#FF0000',
          icon: 'restaurant',
        );
        await categoryRepository.addAsync(category);

        final completer = Completer<void>();
        final receivedData = <ErpCategory?>[];
        late StreamSubscription subscription;

        // Act
        subscription = categoryRepository.watchByObjectId(category.objectId!).listen((cat) {
          receivedData.add(cat);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state
        await completer.future;
        expect(receivedData.first, isNotNull);
        expect(receivedData.first!.objectId, equals(category.objectId));

        // Clean up
        await subscription.cancel();
      });

      test('should watch deleted categories stream', () async {
        // Arrange
        final categories = [
          ErpCategory(name: '分類1', color: '#FF0000', icon: 'restaurant'),
          ErpCategory(name: '分類2', color: '#00FF00', icon: 'transport'),
        ];

        for (final category in categories) {
          await categoryRepository.addAsync(category);
        }

        final completer = Completer<void>();
        final receivedData = <List<ErpCategory>>[];
        late StreamSubscription subscription;

        // Act
        subscription = categoryRepository.watchDeleted().listen((deletedCategories) {
          receivedData.add(deletedCategories);
          if (!completer.isCompleted) {
            completer.complete();
          }
        });

        // Wait for initial state (should be empty)
        await completer.future;
        expect(receivedData.first, isEmpty);

        // Soft delete one category
        await categoryRepository.softDeleteAsync(categories[0].id!);

        // Wait for updates
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(receivedData.last.length, equals(1));
        expect(receivedData.last.first.name, equals('分類1'));

        // Clean up
        await subscription.cancel();
      });
    });

    group('Error Handling', () {
      test('should handle errors gracefully in async operations', () async {
        // Test with invalid data - this should not crash
        try {
          final category = ErpCategory();
          await categoryRepository.addAsync(category);
          // If we get here, the operation succeeded (which is fine)
        } catch (e) {
          // If an error occurs, it should be handled gracefully
          expect(e, isNotNull);
        }
      });

      test('should handle concurrent operations', () async {
        // Arrange
        final categories = List.generate(10, (index) => ErpCategory(
          name: '並發分類 ${index + 1}',
          color: '#FF0000',
          icon: 'restaurant',
        ));

        // Act - Add categories concurrently
        final futures = categories.map((category) => categoryRepository.addAsync(category));
        final ids = await Future.wait(futures);

        // Assert
        expect(ids.length, equals(10));
        expect(ids.every((id) => id > 0), isTrue);

        // Verify all categories were added
        final allCategories = await categoryRepository.getAllAsync();
        expect(allCategories.length, equals(10));
      });
    });
  });
}