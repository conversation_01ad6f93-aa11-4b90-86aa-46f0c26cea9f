# CategoryRepository 測試

本目錄包含 `CategoryRepository` 的單元測試。

## 測試文件

### `category_repository_test.dart`

這個文件包含了 `CategoryRepository` 的全面單元測試，涵蓋以下方面：

#### 1. 構造函數和基本屬性測試

- 驗證 `CategoryRepository` 能正確創建並與 `BoxProvider` 關聯
- 確保能正確訪問 `Talker` 實例

#### 2. ErpCategory 模型測試

- **屬性創建測試**: 驗證 `ErpCategory` 能正確設置各種屬性
- **複製功能測試**: 測試 `copyWith` 方法能正確複製並更新指定屬性
- **JSON 序列化測試**: 驗證 `toJson` 和 `fromJson` 方法的正確性
- **空值處理測試**: 確保 JSON 轉換能正確處理 null 值
- **顏色處理測試**: 測試 `getColor()` 方法能正確解析顏色字符串

#### 3. ObjectId 測試

- **有效性測試**: 驗證生成的 ObjectId 格式正確（24位十六進制字符串）
- **唯一性測試**: 確保每次生成的 ObjectId 都是唯一的

## 測試策略

由於 ObjectBox 需要特定的平台庫支持，這些測試專注於：

1. **單元測試**: 測試不依賴於 ObjectBox 數據庫的邏輯
2. **模型測試**: 驗證 `ErpCategory` 模型的各種功能
3. **工具測試**: 測試 ObjectId 生成等輔助功能

## 運行測試

```bash
# 運行所有測試
flutter test

# 只運行 CategoryRepository 測試
flutter test test/repositories/category_repository_test.dart

# 運行測試並顯示詳細輸出
flutter test --reporter expanded
```

## 測試覆蓋範圍

目前的測試覆蓋了：

- ✅ 構造函數和基本屬性
- ✅ ErpCategory 模型的所有功能
- ✅ ObjectId 生成和驗證
- ✅ JSON 序列化和反序列化
- ✅ 顏色處理邏輯

## 未來改進

為了提供更全面的測試覆蓋，可以考慮：

1. **集成測試**: 使用真實的 ObjectBox 實例進行集成測試
2. **Mock 測試**: 使用 Mockito 模擬 ObjectBox 的行為
3. **性能測試**: 測試大量數據的處理性能
4. **錯誤處理測試**: 測試各種異常情況的處理

## 注意事項

- 這些測試不需要真實的數據庫連接
- 測試專注於業務邏輯而非數據持久化
- 所有測試都是獨立的，不會相互影響
