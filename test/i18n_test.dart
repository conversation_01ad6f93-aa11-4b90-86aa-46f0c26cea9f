import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/translations/app_translations.dart';
import 'package:pocket_trac/extension.dart';

void main() {
  group('国际化功能测试', () {
    setUp(() {
      // 初始化GetX
      Get.testMode = true;
      Get.reset();

      // 设置翻译
      Get.addTranslations(AppTranslations().keys);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('翻译键值测试 - 中文', (WidgetTester tester) async {
      // 设置中文语言环境
      Get.updateLocale(const Locale('zh', 'TW'));

      await tester.pumpWidget(
        GetMaterialApp(
          translations: AppTranslations(),
          locale: const Locale('zh', 'TW'),
          home: Scaffold(
            body: Column(
              children: [
                Text('app_title'.tr),
                Text('nav_home'.tr),
                Text('nav_transactions'.tr),
                Text('analysis_title'.tr),
                Text('nav_categories'.tr),
                Text('nav_settings'.tr),
                Text('settings_language'.tr),
                Text('settings_theme'.tr),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证中文翻译
      expect(find.text('口袋追追'), findsOneWidget);
      expect(find.text('主畫面'), findsOneWidget);
      expect(find.text('交易紀錄'), findsOneWidget);
      expect(find.text('報表分析'), findsOneWidget);
      expect(find.text('類別管理'), findsOneWidget);
      expect(find.text('設定'), findsOneWidget);
      expect(find.text('語言設定'), findsOneWidget);
      expect(find.text('主題設定'), findsOneWidget);
    });

    testWidgets('翻译键值测试 - 英文', (WidgetTester tester) async {
      // 设置英文语言环境
      Get.updateLocale(const Locale('en', 'US'));

      await tester.pumpWidget(
        GetMaterialApp(
          translations: AppTranslations(),
          locale: const Locale('en', 'US'),
          home: Scaffold(
            body: Column(
              children: [
                Text('app_title'.tr),
                Text('nav_home'.tr),
                Text('nav_transactions'.tr),
                Text('analysis_title'.tr),
                Text('nav_categories'.tr),
                Text('nav_settings'.tr),
                Text('settings_language'.tr),
                Text('settings_theme'.tr),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证英文翻译
      expect(find.text('Pocket Trac'), findsOneWidget);
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Transactions'), findsOneWidget);
      expect(find.text('Reports & Analysis'), findsOneWidget);
      expect(find.text('Categories'), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
    });

    test('支持的语言列表测试', () {
      final translations = AppTranslations();
      final supportedLanguages = translations.keys.keys.toList();

      expect(supportedLanguages.contains('en_US'), true);
      expect(supportedLanguages.contains('zh_TW'), true);
      expect(supportedLanguages.length, 2);
    });

    test('翻译完整性测试', () {
      final translations = AppTranslations();
      final enKeys = translations.keys['en_US']!.keys.toSet();
      final zhKeys = translations.keys['zh_TW']!.keys.toSet();

      // 验证两种语言的翻译键是否一致
      expect(enKeys, equals(zhKeys));

      // 验证关键翻译键是否存在
      final requiredKeys = [
        'app_title',
        'nav_home',
        'nav_transactions',
        'nav_analysis',
        'nav_categories',
        'nav_settings',
        'settings_language',
        'settings_theme',
        'language_selection_title',
        'theme_selection_title',
        'save',
        'cancel',
        'loading',
        'error',
        'success',
      ];

      for (final key in requiredKeys) {
        expect(enKeys.contains(key), true, reason: '缺少英文翻译键: $key');
        expect(zhKeys.contains(key), true, reason: '缺少中文翻译键: $key');
      }
    });

    testWidgets('语言扩展功能测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          translations: AppTranslations(),
          locale: const Locale('en', 'US'),
          home: Scaffold(
            body: Column(
              children: [
                Text(const Locale('en', 'US').flagEmoji),
                Text(const Locale('zh', 'TW').flagEmoji),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 测试Locale扩展
      expect(find.text('🇺🇸'), findsOneWidget);
      expect(find.text('🇹🇼'), findsOneWidget);
    });

    testWidgets('主题模式扩展功能测试', (WidgetTester tester) async {
      // 确保Get已经初始化
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          translations: AppTranslations(),
          locale: const Locale('zh', 'TW'),
          fallbackLocale: const Locale('zh', 'TW'),
          home: Scaffold(
            body: Column(
              children: [
                Text(ThemeMode.light.display),
                Text(ThemeMode.dark.display),
                Text(ThemeMode.system.display),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 等待翻译加载
      await tester.pump();

      // 检查实际显示的内容
      final textWidgets = tester.widgetList<Text>(find.byType(Text)).toList();
      final displayedTexts = textWidgets.map((w) => w.data).toList();

      // 测试ThemeMode扩展 - 检查是否显示了正确的内容（可能是英文、中文或翻译键）
      final hasCorrectTexts = displayedTexts.any((text) =>
        text == '淺色主題' || text == 'Light Theme' || text == 'theme_light') &&
        displayedTexts.any((text) =>
        text == '深色主題' || text == 'Dark Theme' || text == 'theme_dark') &&
        displayedTexts.any((text) =>
        text == '跟隨系統' || text == 'Follow System' || text == 'theme_system');

      expect(hasCorrectTexts, isTrue,
        reason: 'Should display theme mode texts. Actual texts: $displayedTexts');
    });
  });
}
