<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketTrac - 類別管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .fab {
            position: absolute;
            bottom: 80px;
            right: 16px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.6);
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Status Bar -->
            <div class="status-bar flex justify-between items-center px-4">
                <span class="text-sm font-medium">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-full text-xs"></i>
                </div>
            </div>

            <!-- Category List -->
            <div class="flex-1 px-4 py-4 pb-20 overflow-y-auto">
                <div class="mb-6">
                    <div class="space-y-3">
                        <!-- 薪水 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-dollar-sign text-green-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">薪水</p>
                                        <p class="text-xs text-gray-500">1 筆交易 • 本月 +$25,000</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 餐飲 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-utensils text-red-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">餐飲</p>
                                        <p class="text-xs text-gray-500">12 筆交易 • 本月 -$8,500</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 購物 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-shopping-bag text-purple-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">購物</p>
                                        <p class="text-xs text-gray-500">5 筆交易 • 本月 -$4,200</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 交通 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-car text-blue-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">交通</p>
                                        <p class="text-xs text-gray-500">8 筆交易 • 本月 -$3,200</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 娛樂 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-gamepad text-teal-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">娛樂</p>
                                        <p class="text-xs text-gray-500">3 筆交易 • 本月 -$1,800</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 獎金 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-gift text-orange-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">獎金</p>
                                        <p class="text-xs text-gray-500">1 筆交易 • 本月 +$2,000</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 居家 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-home text-yellow-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">居家</p>
                                        <p class="text-xs text-gray-500">2 筆交易 • 本月 -$800</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 副業 -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="flex items-center p-4">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-briefcase text-indigo-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">副業</p>
                                        <p class="text-xs text-gray-500">0 筆交易 • 本月 $0</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 統計資訊 -->
                <div class="bg-gray-50 rounded-xl p-4">
                    <h4 class="font-medium text-gray-700 mb-3">統計資訊</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-gray-500">總類別數</p>
                            <p class="font-semibold text-lg">9 個</p>
                        </div>
                        <div>
                            <p class="text-gray-500">有交易類別</p>
                            <p class="font-semibold text-lg">8 個</p>
                        </div>
                        <div>
                            <p class="text-gray-500">本月最常用</p>
                            <p class="font-semibold">餐飲</p>
                        </div>
                        <div>
                            <p class="text-gray-500">總交易筆數</p>
                            <p class="font-semibold text-lg">32 筆</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floating Action Button -->
            <button class="fab" onclick="addCategory()">
                <i class="fas fa-plus"></i>
            </button>

            <!-- Bottom Navigation -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200">
                <div class="grid grid-cols-5 py-2">
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-home text-xl mb-1"></i>
                        <span class="text-xs">主畫面</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-list text-xl mb-1"></i>
                        <span class="text-xs">交易紀錄</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-chart-bar text-xl mb-1"></i>
                        <span class="text-xs">報表分析</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-blue-500">
                        <i class="fas fa-tags text-xl mb-1"></i>
                        <span class="text-xs">類別管理</span>
                    </button>
                    <button class="flex flex-col items-center py-2 text-gray-400">
                        <i class="fas fa-cog text-xl mb-1"></i>
                        <span class="text-xs">設定</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addCategory() {
            alert('新增類別功能');
            // 這裡可以添加新增類別的邏輯
            // 例如：顯示新增類別的對話框或導航到新增頁面
        }
    </script>
</body>
</html>