<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketTrac - 新增類別</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
        }
        .color-option.selected {
            border-color: #ffffff;
            box-shadow: 0 0 0 2px #667eea;
        }
        .icon-option {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s;
        }
        .icon-option.selected {
            border-color: #667eea;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Status Bar -->
            <div class="status-bar flex justify-between items-center px-4">
                <span class="text-sm font-medium">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-full text-xs"></i>
                </div>
            </div>

            <!-- Header -->

            <!-- Form Content -->
            <div class="flex-1 px-4 py-6 pb-20 overflow-y-auto">
                <!-- 類別名稱 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">類別名稱</label>
                    <input type="text" placeholder="輸入類別名稱" value="餐飲"
                           class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- 顏色選擇 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-3">選擇顏色</label>
                    <div class="grid grid-cols-8 gap-3">
                        <div class="color-option selected" style="background-color: #ef4444;"></div>
                        <div class="color-option" style="background-color: #f97316;"></div>
                        <div class="color-option" style="background-color: #eab308;"></div>
                        <div class="color-option" style="background-color: #22c55e;"></div>
                        <div class="color-option" style="background-color: #06b6d4;"></div>
                        <div class="color-option" style="background-color: #3b82f6;"></div>
                        <div class="color-option" style="background-color: #8b5cf6;"></div>
                        <div class="color-option" style="background-color: #ec4899;"></div>
                        <div class="color-option" style="background-color: #64748b;"></div>
                        <div class="color-option" style="background-color: #374151;"></div>
                        <div class="color-option" style="background-color: #991b1b;"></div>
                        <div class="color-option" style="background-color: #92400e;"></div>
                        <div class="color-option" style="background-color: #365314;"></div>
                        <div class="color-option" style="background-color: #0e7490;"></div>
                        <div class="color-option" style="background-color: #1e40af;"></div>
                        <div class="color-option" style="background-color: #6b21a8;"></div>
                    </div>
                </div>

                <!-- 圖示選擇 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-3">選擇圖示</label>
                    <div class="grid grid-cols-6 gap-3">
                        <div class="icon-option selected flex items-center justify-center">
                            <i class="fas fa-utensils text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-car text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-shopping-bag text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-gamepad text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-home text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-heart text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-book text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-plane text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-coffee text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-gas-pump text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-mobile-alt text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-dumbbell text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-cut text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-paw text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-gift text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-tshirt text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-tv text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-tools text-gray-600"></i>
                        </div>
                    </div>
                </div>

                <!-- 備註 -->
            </div>

            <!-- Action Buttons -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-xl font-medium">
                        取消
                    </button>
                    <button class="flex-1 bg-blue-500 text-white py-3 rounded-xl font-medium">
                        儲存類別
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 