<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketTrac - 新增交易</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .tab-active {
            background: white;
            color: #667eea;
        }
        .tab-inactive {
            background: transparent;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Status Bar -->
            <div class="status-bar flex justify-between items-center px-4">
                <span class="text-sm font-medium">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-full text-xs"></i>
                </div>
            </div>

            <!-- Header -->
            <div class="gradient-bg text-white px-4 py-4">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <button class="text-white">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                        <h1 class="text-xl font-bold">新增交易</h1>
                    </div>
                    <button class="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium">
                        儲存
                    </button>
                </div>

                <!-- 交易類型選擇 -->
                <div class="flex bg-white/20 backdrop-blur-sm rounded-lg p-1 mb-4">
                    <button class="flex-1 py-2 rounded-md text-center font-medium tab-inactive">
                        <i class="fas fa-arrow-down mr-2"></i>支出
                    </button>
                    <button class="flex-1 py-2 rounded-md text-center font-medium tab-active">
                        <i class="fas fa-arrow-up mr-2"></i>收入
                    </button>
                </div>
            </div>

            <!-- Form Content -->
            <div class="flex-1 px-4 py-6 pb-20 overflow-y-auto">
                <!-- 金額輸入 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">金額</label>
                    <div class="relative">
                        <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 text-xl">$</span>
                        <input type="number" placeholder="0" 
                               class="w-full bg-white border border-gray-200 rounded-xl px-12 py-4 text-2xl text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <!-- 消費類別 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-3">消費類別</label>
                    <div class="grid grid-cols-4 gap-3">
                        <button class="flex flex-col items-center p-3 bg-white border-2 border-blue-500 rounded-xl">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-utensils text-red-500"></i>
                            </div>
                            <span class="text-xs font-medium text-blue-500">餐飲</span>
                        </button>
                        
                        <button class="flex flex-col items-center p-3 bg-white border border-gray-200 rounded-xl">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-car text-blue-500"></i>
                            </div>
                            <span class="text-xs font-medium text-gray-600">交通</span>
                        </button>
                        
                        <button class="flex flex-col items-center p-3 bg-white border border-gray-200 rounded-xl">
                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-shopping-bag text-purple-500"></i>
                            </div>
                            <span class="text-xs font-medium text-gray-600">購物</span>
                        </button>
                        
                        <button class="flex flex-col items-center p-3 bg-white border border-gray-200 rounded-xl">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-gamepad text-green-500"></i>
                            </div>
                            <span class="text-xs font-medium text-gray-600">娛樂</span>
                        </button>
                        
                        <button class="flex flex-col items-center p-3 bg-white border border-gray-200 rounded-xl">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-home text-yellow-500"></i>
                            </div>
                            <span class="text-xs font-medium text-gray-600">居家</span>
                        </button>
                        
                        <button class="flex flex-col items-center p-3 bg-white border border-gray-200 rounded-xl">
                            <div class="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-heart text-pink-500"></i>
                            </div>
                            <span class="text-xs font-medium text-gray-600">醫療</span>
                        </button>
                        
                        <button class="flex flex-col items-center p-3 bg-white border border-gray-200 rounded-xl">
                            <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-book text-indigo-500"></i>
                            </div>
                            <span class="text-xs font-medium text-gray-600">教育</span>
                        </button>
                        
                        <button class="flex flex-col items-center p-3 bg-white border border-gray-200 rounded-xl">
                            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                                <i class="fas fa-plus text-gray-500"></i>
                            </div>
                            <span class="text-xs font-medium text-gray-600">新增</span>
                        </button>
                    </div>
                </div>

                <!-- 日期時間 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">日期時間</label>
                    <div class="flex space-x-3">
                        <div class="flex-1">
                            <input type="date" value="2024-03-15" 
                                   class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex-1">
                            <input type="time" value="14:30" 
                                   class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>

                <!-- 備註 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">備註</label>
                    <textarea placeholder="添加備註..." 
                              class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 h-24 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>

                <!-- 地點 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">地點 (選填)</label>
                    <button class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 text-left flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-map-marker-alt text-gray-400"></i>
                            <span class="text-gray-500">添加地點</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>

                <!-- 相機/相簿 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">照片 (選填)</label>
                    <div class="flex space-x-3">
                        <button class="flex-1 bg-white border border-gray-200 rounded-xl px-4 py-3 flex items-center justify-center space-x-2">
                            <i class="fas fa-camera text-gray-400"></i>
                            <span class="text-gray-600">拍照</span>
                        </button>
                        <button class="flex-1 bg-white border border-gray-200 rounded-xl px-4 py-3 flex items-center justify-center space-x-2">
                            <i class="fas fa-image text-gray-400"></i>
                            <span class="text-gray-600">相簿</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-xl font-medium">
                        取消
                    </button>
                    <button class="flex-1 bg-blue-500 text-white py-3 rounded-xl font-medium">
                        儲存交易
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 