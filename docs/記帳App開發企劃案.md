# 記帳 App 開發企劃案

## 1. 專案目標

開發一款直觀易用的記帳 App，幫助使用者輕鬆追蹤個人財務收支，培養良好的理財習慣。App 應提供清晰的消費分類、詳細的交易紀錄以及實用的財務報表功能。

## 2. 目標使用者

- 需要管理個人日常開銷的學生、上班族。
- 希望了解自己消費習慣並進行預算規劃的使用者。
- 偏好簡潔操作介面與快速紀錄功能的使用者。

## 3. 主要功能規劃

### 3.1. 交易紀錄 (Orders / Transactions)

- **新增交易**：
  - 記錄金額 (`amount`)
  - 選擇交易日期與時間 (`trigger_at`)
  - 選擇交易類型：支出、收入 (`type` - 例如：1 代表支出, 2 代表收入)
  - 選擇消費類別（關聯至 `Category` - `parent_table`, `parent_object_id`）
  - 填寫備註 (`note`)
  - （可選）記錄消費地點的經緯度 (`latitude`, `longitude`)
- **編輯交易**：修改已記錄的交易資訊。
- **刪除交易**：支援軟刪除 (`deleted_at`)，方便資料恢復。
- **交易列表**：依時間排序顯示所有交易紀錄，可篩選或搜尋。

### 3.2. 消費類別管理 (Categories)

- **新增類別**：
  - 設定類別名稱 (`name`)
  - 選擇類別代表顏色 (`color`)
  - 設定類別排序 (`sort`)
- **編輯類別**：修改類別資訊。
- **刪除類別**：支援軟刪除 (`deleted_at`)。
- **類別列表**：顯示所有自訂及預設類別。

### 3.3. 財務報表與分析

- **收支統計**：按日、週、月、年或自訂區間統計總收入、總支出及結餘。
- **消費結構分析**：以圖表（如圓餅圖、長條圖）展示各消費類別的佔比。
- **趨勢分析**：展示特定時間範圍內收支變化的趨勢圖。

### 3.4. 預算管理（進階功能）

- 設定每月或各類別的預算上限。
- 追蹤預算使用進度，超支時提醒。

### 3.5. 資料管理

- **本地儲存**：交易資料安全儲存在使用者裝置。
- **（可選）雲端同步與備份**：支援將資料同步至雲端，防止資料遺失，並實現跨裝置使用（可利用 `object_id` 進行唯一標識同步）。
- **（可選）資料匯出**：支援將交易資料匯出為 CSV 或其他格式。

## 4. 資料模型設計（基於提供的 JSON 結構）

### 4.1. `Category`（消費類別）

對應 `erp_category.json`

| 欄位          | 資料類型   | 描述                     | 備註                                   |
| ------------- | ---------- | ------------------------ | -------------------------------------- |
| `id`          | Integer    | 本地資料庫主鍵           | 自動遞增                               |
| `object_id`   | String     | 全域唯一識別碼           | 用於同步或外部系統對接                 |
| `name`        | String     | 類別名稱                 |                                        |
| `color`       | String     | 類別代表色 (e.g., #FF5733) |                                        |
| `sort`        | Integer    | 排序順序                 |                                        |
| `created_at`  | DateTime   | 建立時間                 |                                        |
| `updated_at`  | DateTime   | 最後更新時間             |                                        |
| `deleted_at`  | DateTime   | 軟刪除標記（可為 null）  |                                        |

### 4.2. `Order`（交易紀錄）

對應 `erp_order.json`

| 欄位               | 資料類型   | 描述                     | 備註                                   |
| ------------------ | ---------- | ------------------------ | -------------------------------------- |
| `id`               | Integer    | 本地資料庫主鍵           | 自動遞增                               |
| `object_id`        | String     | 全域唯一識別碼           | 用於同步或外部系統對接                 |
| `trigger_at`       | DateTime   | 交易發生時間             |                                        |
| `parent_table`     | String     | 關聯表名稱（固定為 "category"） |                                        |
| `parent_object_id` | String     | 關聯的 Category `object_id` | 外鍵，指向 Category 表的 `object_id`   |
| `type`             | Integer    | 交易類型                 | 例如：1=支出, 2=收入                   |
| `amount`           | Decimal    | 交易金額                 |                                        |
| `note`             | String     | 備註                     |                                        |
| `latitude`         | Double     | 緯度（可為 null）        |                                        |
| `longitude`        | Double     | 經度（可為 null）        |                                        |
| `created_at`       | DateTime   | 記錄建立時間             |                                        |
| `updated_at`       | DateTime   | 記錄最後更新時間         |                                        |
| `deleted_at`       | DateTime   | 軟刪除標記（可為 null）  |                                        |

## 5. 技術棧初步建議

根據您專案的檔案結構，推測您可能正在使用 Flutter 進行開發。

- **前端框架**：Flutter
- **程式語言**：Dart
- **本地資料庫**：
  - **ObjectBox**：您的專案中已包含 `objectbox-model.json` 和 `objectbox.g.dart`，建議繼續使用，它是一款高效能的 NoSQL 物件資料庫。
  - 備選：`sqflite` (SQLite)、`drift` (Moor)
- **狀態管理**：Provider、Riverpod、BLoC/Cubit（根據團隊熟悉度和專案複雜度選擇）
- **UI 設計**：Material Design 或 Cupertino（Flutter 內建），可自訂主題。
- **圖表庫**：`fl_chart` 或其他 Flutter 圖表套件。
- **（可選）後端與雲端同步**：
  - **Firebase**：您的專案已引入 `firebase_core`、`firebase_analytics`、`firebase_crashlytics`。可考慮使用 Firebase Firestore/Realtime Database 進行資料同步，Firebase Authentication 進行使用者認證。
  - 備選：Supabase、自建後端（如 Node.js + Express、Python + Django/Flask）
- **版本控制**：Git

## 6. 開發階段與時程（簡要範例）

1. **階段一：基礎功能開發（4-6 週）**
   - 專案初始化與環境設定
   - 資料庫模型實現（ObjectBox）
   - 交易紀錄 CRUD 功能
   - 消費類別管理 CRUD 功能
   - 基本 UI/UX 設計與實現
2. **階段二：核心報表與進階功能（3-4 週）**
   - 收支統計報表開發
   - 消費結構分析圖表
   - （可選）預算管理功能
3. **階段三：測試、優化與發佈準備（2-3 週）**
   - 單元測試、整合測試
   - 效能優化、錯誤修復
   - （可選）雲端同步功能整合與測試
   - 準備上架相關資料

## 7. 未來展望

- 多帳戶管理
- 定期自動記帳
- 發票掃描記帳
- 與銀行帳戶或信用卡整合
- 更進階的財務建議與分析

---

這份企劃案提供了一個初步的框架。您可以根據實際需求進行調整和細化。
