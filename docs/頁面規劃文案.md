## App 頁面規劃

### 1. 主畫面 (Home / Dashboard)

- **目的**：提供使用者財務狀況總覽，並能快速開始記帳。
- **主要內容**：
  - **當期總覽**：
    - 顯示本月（或自選期間）的總收入、總支出及結餘。
    - 可快速切換查看今日、本週的數據。
  - **快速記帳入口**：
    - 顯眼的「新增交易」按鈕（Floating Action Button 或置於導覽列）。
  - **近期交易**：
    - 列表顯示最近 3-5 筆交易紀錄摘要（日期、類別、金額）。
    - 「查看全部」連結至「交易列表頁」。
  - **消費結構概覽（可選）**：
    - 小型圓餅圖或長條圖簡要顯示本月主要消費類別分佈。
  - **預算進度（若啟用預算功能）**：
    - 顯示主要預算（如總預算或重要類別預算）的使用百分比。
- **導覽**：
  - 底部導覽列：主畫面、交易紀錄、報表分析、類別管理、設定。

### 2. 交易列表頁 (Transaction List)

- **目的**：詳細展示所有交易紀錄，並提供查詢與管理功能。
- **主要內容**：
  - **交易列表**：
    - 依 `trigger_at`（交易發生時間）降冪排列。
    - 每筆紀錄顯示：日期、時間、消費類別名稱、`note`（備註摘要）、`amount`（金額，支出以紅色或負號標示，收入以綠色或正號標示）。
  - **篩選功能**：
    - 按日期區間篩選。
    - 按 `type`（交易類型：支出/收入）篩選。
    - 按 `parent_object_id`（消費類別）篩選。
  - **搜尋功能**：
    - 根據 `note`（備註）或類別名稱關鍵字搜尋。
  - **操作**：
    - 點擊單筆交易進入「交易詳情/編輯頁」。
    - 長按或滑動可進行快速刪除（軟刪除 `deleted_at`）。
  - **頂部總計**：顯示當前篩選條件下的總收入與總支出。

### 3. 新增 / 編輯交易頁 (Add / Edit Transaction)

- **目的**：讓使用者能快速、準確地記錄或修改一筆交易。
- **主要內容（表單形式）**：
  - **金額（`amount`）**：數字輸入欄位。
  - **交易類型（`type`）**：選擇器（支出 / 收入）。
  - **消費類別（`parent_object_id`）**：
    - 選擇器，從 `Category` 列表中選擇。
    - 可快速新增類別的入口。
  - **日期與時間（`trigger_at`）**：日期時間選擇器，預設為當前時間。
  - **備註（`note`）**：文字輸入欄位（可選）。
  - **地點（`latitude`, `longitude`）**：
    - 可選功能，點擊可開啟地圖選擇或自動獲取當前位置。
    - 顯示已選地點名稱或經緯度。
  - **操作按鈕**：
    - 「儲存」按鈕。
    - 若為編輯模式，則有「刪除」按鈕（軟刪除 `deleted_at`）。

### 4. 消費類別管理頁 (Category Management)

- **目的**：讓使用者管理自己的消費類別。
- **主要內容**：
  - **類別列表**：
    - 顯示所有 `Category`（依 `sort` 排序，再依 `name` 排序）。
    - 每筆類別顯示：`name`（類別名稱）、`color`（代表色標示）。
  - **操作**：
    - 「新增類別」按鈕，導向「新增/編輯類別頁」。
    - 點擊單筆類別進入「新增/編輯類別頁」（編輯模式）。
    - 可拖曳調整類別排序（`sort`）。
    - 滑動或長按可刪除類別（軟刪除 `deleted_at`，需考慮關聯交易的處理方式，如提示使用者或將關聯交易的類別設為「未分類」）。

### 5. 新增 / 編輯類別頁 (Add / Edit Category)

- **目的**：讓使用者新增或修改消費類別的詳細資訊。
- **主要內容（表單形式）**：
  - **類別名稱（`name`）**：文字輸入欄位。
  - **代表顏色（`color`）**：顏色選擇器。
  - **排序（`sort`）**：數字輸入或系統自動處理（可選，若在列表頁已支援拖曳排序，此處可不顯示或僅顯示當前排序值）。
  - **操作按鈕**：
    - 「儲存」按鈕。
    - 若為編輯模式，則有「刪除」按鈕（軟刪除 `deleted_at`）。

### 6. 財務報表與分析頁 (Reports & Analysis)

- **目的**：以視覺化方式呈現財務數據，幫助使用者分析消費習慣。
- **主要內容（可使用頁籤 Tabs 或分區塊顯示）**：
  - **時間區間選擇器**：通用於各報表，可選擇日、週、月、年或自訂日期範圍。
  - **A. 收支統計**：
    - 顯示所選區間內的總收入、總支出、淨結餘。
    - 可考慮以簡單的數字或進度條呈現。
  - **B. 消費結構分析**：
    - **圖表**：
      - 圓餅圖：顯示各消費類別（`Category`）在總支出中的佔比。
      - 長條圖：顯示各消費類別的具體支出金額，按金額大小排序。
    - **列表**：輔助圖表，列出各類別名稱、金額、佔比。
  - **C. 收支趨勢分析**：
    - **圖表**：
      - 折線圖或堆疊長條圖：展示選定時間範圍內（如過去 6 個月、過去一年），每日/每週/每月的收入與支出變化趨勢。

### 7. 預算管理頁 (Budget Management - 進階功能)

- **目的**：協助使用者設定與追蹤預算。
- **主要內容**：
  - **預算設定**：
    - 設定總預算（例如：每月總支出上限）。
    - 設定各消費類別的預算上限（可選）。
  - **預算追蹤**：
    - 列表或卡片形式顯示各預算項目。
    - 每個項目包含：預算名稱、預算金額、已用金額、剩餘金額、進度條。
    - 超支項目以醒目顏色提示。
  - **週期設定**：選擇預算週期（如每月、每年）。

### 8. 設定頁 (Settings)

- **目的**：提供 App 相關設定與資料管理功能。
- **主要內容**：
  - **使用者帳戶（若有雲端同步）**：
    - 登入/註冊/登出。
    - 帳戶資訊。
  - **資料管理**：
    - **雲端同步與備份（`object_id` 相關）**：
      - 啟用/停用雲端同步。
      - 手動同步按鈕。
      - 上次同步時間。
    - **資料匯出**：
      - 選擇匯出格式（如 CSV）。
      - 執行匯出操作。
    - **資料清除/重設（謹慎操作）**。
  - **App 設定**：
    - **外觀主題**：淺色/深色模式。
    - **通知設定**：記帳提醒、超支提醒等。
    - **預設貨幣**（若需支援多幣種）。
  - **關於**：
    - App 版本資訊。
    - 開發者資訊/感謝名單。
    - 隱私權政策/服務條款連結。
