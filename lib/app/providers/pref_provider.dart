import 'dart:async';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pocket_trac/extension.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../enums.dart';
import 'box_provider.dart';

class PrefProvider extends GetxService {
  final _disposable = Completer();
  final BoxProvider boxProvider;
  final DeviceInfoPlugin deviceInfo;
  final PackageInfo packageInfo;
  Talker get talker => boxProvider.talker;

  // 响应式主题模式
  final _currentThemeMode = ThemeMode.system.obs;

  // 响应式语言设置
  final _currentLocale = const Locale('zh', 'TW').obs;

  GetStorage get defaultBox => boxProvider.getGsBox(Boxes.settings.name);

  PrefProvider({
    required this.boxProvider,
    required this.deviceInfo,
    required this.packageInfo,
  });

  @override
  void onInit() {
    super.onInit();
    // 初始化响应式变量
    defaultBox.watch('themeMode').takeUntil(_disposable.future).listen((value) {
      _currentThemeMode.value = themeMode;
    });
    defaultBox.watch('locale').takeUntil(_disposable.future).listen((value) {
      _currentLocale.value = locale;
      // 更新應用語言 - 添加錯誤處理
      try {
        Get.updateLocale(locale);
      } catch (e, s) {
        talker.error(
          'Failed to update locale: $e',
          e,
          s,
        );
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    // 確保清理完成
    if (!_disposable.isCompleted) {
      _disposable.complete();
    }
  }

  /// 获取当前主题模式（响应式）
  ThemeMode get currentThemeMode => _currentThemeMode.value;

  /// 获取当前语言设置（响应式）
  Locale get currentLocale => _currentLocale.value;

  ThemeMode get themeMode {
    final mode = defaultBox.read('themeMode');
    if (mode == null) return ThemeMode.system;
    return ThemeMode.values.firstWhere(
      (e) => e.name == mode,
      orElse: () => ThemeMode.system,
    );
  }

  set themeMode(ThemeMode value) {
    // 持久化存储
    defaultBox.write('themeMode', value.name);
  }

  Locale get locale {
    final localeString = defaultBox.read('locale');
    if (localeString == null) return const Locale('zh', 'TW');

    final parts = localeString.split('_');
    if (parts.length == 2) {
      return Locale(parts[0], parts[1]);
    }
    return const Locale('zh', 'TW');
  }

  set locale(Locale value) {
    // 持久化存储 - 處理 countryCode 可能為 null 的情況
    final localeString = value.countryCode != null
        ? '${value.languageCode}_${value.countryCode}'
        : value.languageCode;
    defaultBox.write('locale', localeString);
  }

  /// 获取当前语言显示名称
  String get currentLocaleDisplayName {
    return getLocaleDisplayName(_currentLocale.value);
  }

  /// 获取语言显示名称
  String getLocaleDisplayName(Locale locale) {
    switch ('${locale.languageCode}_${locale.countryCode}') {
      case 'en_US':
        return 'language_english'.tr;
      case 'zh_TW':
        return 'language_chinese'.tr;
      default:
        return 'language_chinese'.tr;
    }
  }

  /// 获取支持的语言列表
  List<Locale> get supportedLocales => const [
        Locale('en', 'US'),
        Locale('zh', 'TW'),
      ];
}
