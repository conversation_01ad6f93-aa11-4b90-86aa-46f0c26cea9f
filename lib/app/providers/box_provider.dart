import 'package:get_storage/get_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:path/path.dart' as p;

import '../../objectbox.g.dart';

class BoxProvider {
  var _namespace = '';
  set namespace(String value) => _namespace = value;

  final Talker talker;

  /// The Store of this app.
  Store? _store;

  /// usage:
  /// final box = boxProvider.store.box<User>();
  Store get store => _store!;

  BoxProvider({
    required this.talker,
  });

  /// call this method to initialize the store after namespace set.
  Future<void> init() async {
    if (_store == null || store.isClosed()) {
      _store = await _createStore();
    }
  }

  void close() {
    store.close();
    _store = null;
  }

  Future<Store> _createStore() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final path = p.join(docsDir.path, _namespace);
    talker.debug('[BoxProvider] path: $path');
    if (Store.isOpen(path)) {
      // applicable when store is from other isolate
      return Store.attach(getObjectBoxModel(), path);
    }
    return openStore(directory: path);
  }

  Future<bool> initGsBox(String name) {
    final fullName = _getFullName(name);
    return GetStorage.init(fullName);
  }

  GetStorage getGsBox(String name, {bool withNamespace = true}) {
    final fullName = _getFullName(name, withNamespace);
    return GetStorage(fullName);
  }

  String _getFullName(String name, [bool withNamespace = true]) {
    Iterable<String> children([bool withNamespace = true]) sync* {
      yield name;
      if (withNamespace && _namespace.isNotEmpty) {
        yield _namespace;
      }
    }

    return children(withNamespace).join('.');
  }

  Iterable<Map<String, dynamic>> getGs(String table) {
    final box = getGsBox(table);
    return box.getValues<Iterable<Map<String, dynamic>>>();
  }

  Future<void> putGs(Iterable<Map<String, dynamic>> it, String table) async {
    final box = getGsBox(table);
    for (var element in it) {
      if (element.containsKey('id')) {
        box.write('${element['id']}', element);
      }
    }
    await box.save();
  }

  Future<void> delGs(Iterable<int> ids, String table) async {
    final box = getGsBox(table);
    for (var id in ids) {
      box.remove('$id');
    }
    await box.save();
  }

  Future<void> eraseGs(String table) async {
    final box = getGsBox(table);
    await box.erase();
  }

  Future<void> delOb<T>(Iterable<int> ids) async {
    try {
      final box = store.box<T>();
      await box
          .removeManyAsync(ids.where((e) => e != 0).toList(growable: false));
    } catch (e, s) {
      talker.error('$e', e, s);
    }
  }

  Future<Iterable<T>> putOb<T>(Iterable<T> entities) async {
    try {
      final box = store.box<T>();
      return await box.putAndGetManyAsync(entities.toList(growable: false));
    } catch (e, s) {
      talker.error('$e', e, s);
    }
    return [];
  }
}
