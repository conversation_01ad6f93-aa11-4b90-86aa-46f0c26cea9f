import 'dart:async';

import 'package:objectid/objectid.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../objectbox.g.dart';
import '../models/erp_category.dart';
import '../providers/box_provider.dart';

class CategoryRepository {
  final BoxProvider boxProvider;
  Talker get talker => boxProvider.talker;
  Box<ErpCategory> get box => Box<ErpCategory>(boxProvider.store);

  CategoryRepository(this.boxProvider);

  // 異步獲取所有類別
  Future<List<ErpCategory>> getAllAsync({bool includeDeleted = false}) async {
    try {
      if (includeDeleted) {
        // 返回所有類別，包括已刪除的
        return await box.getAllAsync();
      } else {
        // 僅返回未刪除的類別
        final query = box.query(ErpCategory_.deletedAt.isNull()).build();
        final results = await query.findAsync();
        query.close();
        return results;
      }
    } catch (e, s) {
      talker.error('Failed to get all categories async: $e', e, s);
      rethrow;
    }
  }

  // 異步按ID獲取類別
  Future<ErpCategory?> getByIdAsync(int id) async {
    try {
      return await box.getAsync(id);
    } catch (e, s) {
      talker.error('Failed to get category by id async: $e', e, s);
      rethrow;
    }
  }

  // 異步按 UUID 獲取類別
  Future<ErpCategory?> getByObjectIdAsync(String objectId) async {
    try {
      final query = box.query(ErpCategory_.objectId.equals(objectId)).build();
      final results = await query.findAsync();
      query.close();
      return results.isNotEmpty ? results.first : null;
    } catch (e, s) {
      talker.error('Failed to get category by objectId async: $e', e, s);
      rethrow;
    }
  }

  // 異步按名稱搜尋類別
  Future<List<ErpCategory>> searchByNameAsync(String name) async {
    try {
      final query = box
          .query(ErpCategory_.name.contains(name) &
              ErpCategory_.deletedAt.isNull())
          .build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to search categories by name async: $e', e, s);
      rethrow;
    }
  }

  // 異步保存類別（新增或更新）
  Future<int> saveAsync(ErpCategory category) async {
    try {
      final future = category.id == null ? addAsync : updateAsync;
      return await future(category);
    } catch (e, s) {
      talker.error('Failed to save category async: $e', e, s);
      rethrow;
    }
  }

  // 異步新增類別
  Future<int> addAsync(ErpCategory category) async {
    try {
      // 設置創建時間和UUID（如果未提供）
      final now = DateTime.now();
      category.createdAt ??= now;
      category.updatedAt = now;
      category.objectId ??= ObjectId().hexString;

      final id = await box.putAsync(category);
      category.id = id; // 確保返回的ID被設置到物件中
      return id;
    } catch (e, s) {
      talker.error('Failed to add category async: $e', e, s);
      rethrow;
    }
  }

  // 異步更新類別
  Future<int> updateAsync(ErpCategory category) async {
    try {
      // 確保更新時間已設置
      category.updatedAt = DateTime.now();

      return await box.putAsync(category);
    } catch (e, s) {
      talker.error('Failed to update category async: $e', e, s);
      rethrow;
    }
  }

  // 異步批量添加或更新類別
  Future<List<int>> putManyAsync(List<ErpCategory> categories) async {
    try {
      final now = DateTime.now();
      for (final category in categories) {
        // 如果是新物件，設置創建時間和UUID
        if (category.id == null) {
          category.createdAt ??= now;
          category.objectId ??= ObjectId().hexString;
        }
        category.updatedAt = now;
      }

      return await box.putManyAsync(categories);
    } catch (e, s) {
      talker.error('Failed to put many categories async: $e', e, s);
      rethrow;
    }
  }

  // 異步軟刪除類別（設置刪除時間而不是實際刪除）
  Future<bool> softDeleteAsync(int id) async {
    try {
      final category = await box.getAsync(id);
      if (category != null) {
        category.deletedAt = DateTime.now();
        await box.putAsync(category);
        return true;
      }
      return false;
    } catch (e, s) {
      talker.error('Failed to soft delete category async: $e', e, s);
      rethrow;
    }
  }

  // 異步恢復已軟刪除的類別
  Future<bool> restoreAsync(int id) async {
    try {
      final category = await box.getAsync(id);
      if (category != null && category.deletedAt != null) {
        category.deletedAt = null;
        await box.putAsync(category);
        return true;
      }
      return false;
    } catch (e, s) {
      talker.error('Failed to restore category async: $e', e, s);
      rethrow;
    }
  }

  // 異步硬刪除類別（真正從資料庫中刪除）
  Future<bool> hardDeleteAsync(int id) async {
    try {
      return await box.removeAsync(id);
    } catch (e, s) {
      talker.error('Failed to hard delete category async: $e', e, s);
      rethrow;
    }
  }

  // 異步刪除所有類別
  Future<int> deleteAllAsync() async {
    try {
      return await box.removeAllAsync();
    } catch (e, s) {
      talker.error('Failed to delete all categories async: $e', e, s);
      rethrow;
    }
  }

  // 異步獲取類別總數
  Future<int> countAsync({bool includeDeleted = false}) async {
    try {
      if (includeDeleted) {
        // 獲取所有類別並計算數量
        final categories = await box.getAllAsync();
        return categories.length;
      } else {
        // 使用查詢來計算未刪除的類別數量
        final categories = await getAllAsync(includeDeleted: false);
        return categories.length;
      }
    } catch (e, s) {
      talker.error('Failed to count categories async: $e', e, s);
      rethrow;
    }
  }

  // ==================== 串流功能 ====================

  /// 監聽所有類別變更的串流（包括已刪除的）
  /// 當資料庫中的類別資料發生任何變更時，會自動發出最新的類別列表
  Stream<List<ErpCategory>> watchAll() {
    try {
      return box
          .query()
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchAll stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchAll stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽未刪除類別變更的串流
  /// 當資料庫中的未刪除類別資料發生變更時，會自動發出最新的類別列表
  Stream<List<ErpCategory>> watchActive() {
    try {
      return box
          .query(ErpCategory_.deletedAt.isNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchActive stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchActive stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定名稱搜尋結果的串流
  /// 當符合搜尋條件的類別資料發生變更時，會自動發出最新的搜尋結果
  /// [name] 搜尋的類別名稱（支援部分匹配）
  Stream<List<ErpCategory>> watchByName(String name) {
    try {
      return box
          .query(ErpCategory_.name.contains(name) &
              ErpCategory_.deletedAt.isNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchByName stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchByName stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽類別數量變更的串流
  /// 當類別數量發生變更時，會自動發出最新的數量
  /// [includeDeleted] 是否包含已刪除的類別
  Stream<int> watchCount({bool includeDeleted = false}) {
    try {
      return includeDeleted
          ? box
              .query()
              .watch(triggerImmediately: true)
              .map((query) => query.count())
          : box
              .query(ErpCategory_.deletedAt.isNull())
              .watch(triggerImmediately: true)
              .map((query) => query.count())
              .handleError((error, stackTrace) {
        talker.error('Error in watchCount stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchCount stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定 ID 類別變更的串流
  /// 當指定 ID 的類別資料發生變更時，會自動發出最新的類別資料
  /// [id] 要監聽的類別 ID
  Stream<ErpCategory?> watchById(int id) {
    try {
      return box
          .query(ErpCategory_.id.equals(id))
          .watch(triggerImmediately: true)
          .map((query) {
        final results = query.find();
        return results.isNotEmpty ? results.first : null;
      }).handleError((error, stackTrace) {
        talker.error('Error in watchById stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchById stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定 ObjectId 類別變更的串流
  /// 當指定 ObjectId 的類別資料發生變更時，會自動發出最新的類別資料
  /// [objectId] 要監聽的類別 ObjectId
  Stream<ErpCategory?> watchByObjectId(String objectId) {
    try {
      return box
          .query(ErpCategory_.objectId.equals(objectId))
          .watch(triggerImmediately: true)
          .map((query) {
        final results = query.find();
        return results.isNotEmpty ? results.first : null;
      }).handleError((error, stackTrace) {
        talker.error('Error in watchByObjectId stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchByObjectId stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽已刪除類別的串流
  /// 當已刪除的類別資料發生變更時，會自動發出最新的已刪除類別列表
  Stream<List<ErpCategory>> watchDeleted() {
    try {
      return box
          .query(ErpCategory_.deletedAt.notNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchDeleted stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchDeleted stream: $e', e, s);
      rethrow;
    }
  }
}
