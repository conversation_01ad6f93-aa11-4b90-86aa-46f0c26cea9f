import 'package:flutter/material.dart';
import 'package:pocket_trac/extension.dart';

import '../models/erp_category.dart';
import '../../colors.dart';

class CategoryItem extends StatelessWidget {
  final ErpCategory category;
  final VoidCallback? onTap;

  const CategoryItem({
    required this.category,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    return ListTile(
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      leading: _buildCategoryIcon(),
      title: Text(
        category.name ?? '',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: textTheme.bodyLarge?.color ??
                 (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
        ),
      ),
      subtitle: Text(
        _buildSubtitleText(),
        style: TextStyle(
          fontSize: 12,
          color: textTheme.bodyMedium?.color ??
                 (isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary),
        ),
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: isDark ? ErpColors.darkTextSecondary : ErpColors.textHint,
      ),
    );
  }

  Widget _buildCategoryIcon() {
    final categoryColor = category.getColor();
    final lightColor = categoryColor.withOpacity(0.1);

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: lightColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        category.getIcon(),
        color: categoryColor,
        size: 20,
      ),
    );
  }

  String _buildSubtitleText() {
    final transactionCount = category.children.length;
    final amount = category.amount ?? 0;
    final isIncome = amount > 0;
    final amountText = amount == 0
        ? '\$0'
        : '${isIncome ? '+' : '-'}\$${amount.abs().toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';

    return '$transactionCount 筆交易 • 本月 $amountText';
  }
}
