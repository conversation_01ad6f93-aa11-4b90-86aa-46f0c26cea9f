import 'package:flutter/material.dart';

class CategoryIcons {
  // 記帳分類圖標映射
  static final Map<String, IconData> iconMap = {
    // === 收入類別 ===
    '${Icons.work}': Icons.work, // 薪資
    '${Icons.trending_up}': Icons.trending_up, // 投資收益
    '${Icons.business}': Icons.business, // 兼職
    '${Icons.card_giftcard}': Icons.card_giftcard, // 獎金/紅利
    '${Icons.account_balance}': Icons.account_balance, // 銀行利息
    '${Icons.sell}': Icons.sell, // 出售物品
    '${Icons.volunteer_activism}': Icons.volunteer_activism, // 補助/津貼

    // === 飲食類別 ===
    '${Icons.restaurant}': Icons.restaurant, // 正餐
    '${Icons.local_cafe}': Icons.local_cafe, // 咖啡/茶飲
    '${Icons.local_bar}': Icons.local_bar, // 酒類
    '${Icons.fastfood}': Icons.fastfood, // 速食
    '${Icons.lunch_dining}': Icons.lunch_dining, // 午餐
    '${Icons.dinner_dining}': Icons.dinner_dining, // 晚餐
    '${Icons.breakfast_dining}': Icons.breakfast_dining, // 早餐
    '${Icons.icecream}': Icons.icecream, // 甜點/冰品
    '${Icons.local_pizza}': Icons.local_pizza, // 外賣

    // === 交通類別 ===
    '${Icons.directions_car}': Icons.directions_car, // 汽車
    '${Icons.directions_bus}': Icons.directions_bus, // 公車
    '${Icons.directions_subway}': Icons.directions_subway, // 地鐵
    '${Icons.local_taxi}': Icons.local_taxi, // 計程車
    '${Icons.local_gas_station}': Icons.local_gas_station, // 加油
    '${Icons.two_wheeler}': Icons.two_wheeler, // 機車
    '${Icons.pedal_bike}': Icons.pedal_bike, // 腳踏車
    '${Icons.flight}': Icons.flight, // 飛機
    '${Icons.directions_boat}': Icons.directions_boat, // 船
    '${Icons.local_parking}': Icons.local_parking, // 停車費

    // === 購物類別 ===
    '${Icons.shopping_bag}': Icons.shopping_bag, // 購物
    '${Icons.checkroom}': Icons.checkroom, // 服裝
    '${Icons.phone_android}': Icons.phone_android, // 電子產品
    '${Icons.book}': Icons.book, // 書籍
    '${Icons.home_repair_service}': Icons.home_repair_service, // 日用品
    '${Icons.pets}': Icons.pets, // 寵物用品
    '${Icons.local_grocery_store}': Icons.local_grocery_store, // 生鮮食品
    '${Icons.store}': Icons.store, // 超市

    // === 娛樂類別 ===
    '${Icons.movie}': Icons.movie, // 電影
    '${Icons.sports_esports}': Icons.sports_esports, // 遊戲
    '${Icons.travel_explore}': Icons.travel_explore, // 旅遊
    '${Icons.sports_soccer}': Icons.sports_soccer, // 運動
    '${Icons.music_note}': Icons.music_note, // 音樂/演唱會
    '${Icons.local_activity}': Icons.local_activity, // 娛樂活動
    '${Icons.casino}': Icons.casino, // 博弈

    // === 健康醫療 ===
    '${Icons.local_hospital}': Icons.local_hospital, // 醫療
    '${Icons.local_pharmacy}': Icons.local_pharmacy, // 藥品
    '${Icons.fitness_center}': Icons.fitness_center, // 健身
    '${Icons.spa}': Icons.spa, // 保養/SPA
    '${Icons.security}': Icons.security, // 保險
    '${Icons.healing}': Icons.healing, // 看病

    // === 居住類別 ===
    '${Icons.home}': Icons.home, // 房租/房貸
    '${Icons.electrical_services}': Icons.electrical_services, // 電費
    '${Icons.water_drop}': Icons.water_drop, // 水費
    '${Icons.wifi}': Icons.wifi, // 網路費
    '${Icons.local_fire_department}': Icons.local_fire_department, // 瓦斯費
    '${Icons.cleaning_services}': Icons.cleaning_services, // 清潔費
    '${Icons.build}': Icons.build, // 維修費
    '${Icons.security_update_good}': Icons.security_update_good, // 管理費

    // === 教育類別 ===
    '${Icons.school}': Icons.school, // 學費
    '${Icons.library_books}': Icons.library_books, // 教材
    '${Icons.computer}': Icons.computer, // 線上課程
    '${Icons.assignment}': Icons.assignment, // 考試/證照費
    '${Icons.psychology}': Icons.psychology, // 補習/培訓

    // === 金融相關 ===
    '${Icons.attach_money}': Icons.attach_money, // 現金
    '${Icons.credit_card}': Icons.credit_card, // 信用卡
    '${Icons.savings}': Icons.savings, // 儲蓄
    '${Icons.monetization_on}': Icons.monetization_on, // 投資
    '${Icons.currency_exchange}': Icons.currency_exchange, // 匯款/轉帳
    '${Icons.payment}': Icons.payment, // 貸款

    // === 其他類別 ===
    '${Icons.redeem}': Icons.redeem, // 禮品
    '${Icons.favorite}': Icons.favorite, // 捐贈/慈善
    '${Icons.celebration}': Icons.celebration, // 節日/慶祝
    '${Icons.face}': Icons.face, // 個人護理
    '${Icons.phone}': Icons.phone, // 通訊費
    '${Icons.subscriptions}': Icons.subscriptions, // 訂閱服務
    '${Icons.receipt}': Icons.receipt, // 稅費
    '${Icons.miscellaneous_services}': Icons.miscellaneous_services, // 其他服務

    // === 通用圖標 ===
    '${Icons.category}': Icons.category,
    '${Icons.add}': Icons.add,
    '${Icons.remove}': Icons.remove,
    '${Icons.edit}': Icons.edit,
    '${Icons.delete}': Icons.delete,
    '${Icons.settings}': Icons.settings,
    '${Icons.money_off}': Icons.money_off, // 支出
  };

  /// 根据分类名称获取图标
  static IconData getIcon(String? iconName) {
    if (hasIcon(iconName)) {
      return iconMap[iconName]!;
    }
    return availableIcons.first;
  }

  static String getIconName(IconData icon) {
    return iconMap.entries
        .firstWhere((entry) => entry.value == icon, orElse: () => MapEntry('', Icons.category))
        .key;
  }

  static Iterable<IconData> get availableIcons => iconMap.values;

  /// 獲取所有可用圖標名稱
  static Iterable<String> get getAllIconNames => iconMap.keys;

  /// 檢查圖標是否存在
  static bool hasIcon(String? iconName) {
    if (iconName == null || iconName.isEmpty) {
      return false;
    }
    return iconMap.containsKey(iconName);
  }
}
