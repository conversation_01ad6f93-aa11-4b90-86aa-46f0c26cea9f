import 'package:get_storage/get_storage.dart';

import '../../enums.dart';
import '../providers/box_provider.dart';

extension BoxProviderExtension on BoxProvider {
  Future<void> initBoxes([namespace = '']) async {
    namespace = namespace;
    await init();
    for (var box in Boxes.values) {
      await initGsBox(box.name);
    }
  }

  GetStorage getBox(Boxes box, {withNamespace = true}) {
    return getGsBox(box.name, withNamespace: withNamespace);
  }
}
