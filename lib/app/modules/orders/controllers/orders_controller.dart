import 'package:get/get.dart';
import 'package:pocket_trac/app/models/erp_order.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/providers/pref_provider.dart';
import 'package:pocket_trac/app/repositories/order_repository.dart';
import 'package:talker_flutter/talker_flutter.dart';

class OrdersController extends GetxController with StateMixin<List<ErpOrder>> {
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => boxProvider.talker;

  // Repository
  final OrderRepository orderRepository;
  final PrefProvider prefProvider;

  // Observable data
  final orders = <ErpOrder>[].obs;
  final groupedOrders = <String, List<ErpOrder>>{}.obs;
  final searchQuery = ''.obs;
  final selectedFilter = OrderFilter().obs;

  // Statistics
  final totalIncome = 0.0.obs;
  final totalExpense = 0.0.obs;
  final totalBalance = 0.0.obs;

  // Pagination
  final currentPage = 0.obs;
  final hasMoreData = true.obs;
  final isLoadingMore = false.obs;

  OrdersController({
    required this.orderRepository,
    required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _loadOrders();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /// 載入訂單數據
  Future<void> _loadOrders() async {
    try {
      change(null, status: RxStatus.loading());

      final filter = selectedFilter.value.copyWith(
        limit: 20,
        offset: 0,
        sortBy: 'triggerAt',
        ascending: false,
      );

      final orderList = await orderRepository.getOrdersAsync(filter);
      orders.assignAll(orderList);

      _groupOrdersByDate();
      _calculateStatistics();

      change(orderList, status: RxStatus.success());
      currentPage.value = 0;
      hasMoreData.value = orderList.length >= 20;

      talker.debug('Loaded ${orderList.length} orders');
    } catch (e, s) {
      talker.error('Failed to load orders: $e', e, s);
      change(null, status: RxStatus.error('載入訂單失敗: $e'));
    }
  }

  /// 刷新數據
  Future<void> onRefresh() async {
    await _loadOrders();
  }

  /// 載入更多數據
  Future<void> onEndScroll() async {
    if (isLoadingMore.value || !hasMoreData.value) return;

    try {
      isLoadingMore.value = true;

      final nextPage = currentPage.value + 1;
      final filter = selectedFilter.value.copyWith(
        limit: 20,
        offset: nextPage * 20,
        sortBy: 'triggerAt',
        ascending: false,
      );

      final moreOrders = await orderRepository.getOrdersAsync(filter);

      if (moreOrders.isNotEmpty) {
        orders.addAll(moreOrders);
        _groupOrdersByDate();
        _calculateStatistics();
        currentPage.value = nextPage;
        hasMoreData.value = moreOrders.length >= 20;
      } else {
        hasMoreData.value = false;
      }

      talker.debug('Loaded ${moreOrders.length} more orders');
    } catch (e, s) {
      talker.error('Failed to load more orders: $e', e, s);
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// 按日期分組訂單
  void _groupOrdersByDate() {
    final Map<String, List<ErpOrder>> grouped = {};

    for (final order in orders) {
      final date = order.triggerAt;
      if (date != null) {
        final dateKey = _getDateKey(date);
        grouped.putIfAbsent(dateKey, () => []);
        grouped[dateKey]!.add(order);
      }
    }

    // 按日期排序每組內的訂單
    grouped.forEach((key, orderList) {
      orderList.sort((a, b) {
        final aTime = a.triggerAt ?? DateTime.now();
        final bTime = b.triggerAt ?? DateTime.now();
        return bTime.compareTo(aTime); // 最新的在前
      });
    });

    groupedOrders.assignAll(grouped);
  }

  /// 獲取日期分組的鍵值
  String _getDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final orderDate = DateTime(date.year, date.month, date.day);

    if (orderDate == today) {
      return 'transactions_today'.tr;
    } else if (orderDate == yesterday) {
      return 'transactions_yesterday'.tr;
    } else {
      return '${date.month}/${date.day}';
    }
  }

  /// 計算統計數據
  void _calculateStatistics() {
    double income = 0.0;
    double expense = 0.0;

    for (final order in orders) {
      final amount = order.amount ?? 0.0;
      final type = order.type ?? 0;

      if (type == 2) { // 收入
        income += amount;
      } else if (type == 1) { // 支出
        expense += amount;
      }
    }

    totalIncome.value = income;
    totalExpense.value = expense;
    totalBalance.value = income - expense;
  }

  /// 搜尋訂單
  void searchOrders(String query) {
    searchQuery.value = query;
    final filter = selectedFilter.value.copyWith(
      noteSearch: query.isEmpty ? null : query,
    );
    selectedFilter.value = filter;
    _loadOrders();
  }

  /// 應用篩選
  void applyFilter(OrderFilter filter) {
    selectedFilter.value = filter;
    _loadOrders();
  }

  /// 清除篩選
  void clearFilter() {
    selectedFilter.value = const OrderFilter();
    searchQuery.value = '';
    _loadOrders();
  }
}
