import 'package:get/get.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/providers/pref_provider.dart';
import 'package:talker_flutter/talker_flutter.dart';

class SettingsController extends GetxController with StateMixin<String> {
  final PrefProvider prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Talker get talker => boxProvider.talker;

  SettingsController({required this.prefProvider});

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      // Your refresh logic here
      change('', status: RxStatus.success());
    } catch (e, s) {
      talker.error('$e', e, s);
      // Handle error
      change('', status: RxStatus.error('Error: $e'));
    }
  }
}
