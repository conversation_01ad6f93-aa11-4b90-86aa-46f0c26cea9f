import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/utils/category_colors.dart';
import 'package:pocket_trac/app/utils/category_icons.dart';
import 'package:pocket_trac/extension.dart';

import '../../../../colors.dart';
import '../controllers/category_detail_controller.dart';

class CategoryDetailView extends GetView<CategoryDetailController> {
  const CategoryDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    Get.lazyPut<CategoryDetailController>(
      () => CategoryDetailController(
        categoryRepository: Get.find(),
      ),
    );
    return LayoutBuilder(
      builder: (context, constraints) {
        return _buildMain();
      },
    );
  }

  Widget _buildMain() {
    return Builder(
      builder: (context) {
        return controller.obx(
          (state) => _buildBody(context),
          onLoading: const Center(child: CircularProgressIndicator()),
          onError: (error) => Center(
            child: Text(
              'Error: $error',
              style: const TextStyle(color: ErpColors.error),
            ),
          ),
        );
      },
    );
  }

  /// 构建主体内容
  Widget _buildBody(BuildContext context) {
    Iterable<Widget> getChildren() sync* {
      yield _buildNameField(context);
      yield const SizedBox(height: 24);
      yield _buildColorSection(context);
      yield _buildIconSection(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 表单内容区域
        Flexible(
          fit: FlexFit.loose,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: controller.formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: getChildren().toList(growable: false),
              ),
            ),
          ),
        ),
        // 底部操作按钮
        _buildBottomActions(context),
      ],
    );
  }

  /// 构建分类名称输入框
  Widget _buildNameField(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '類別名稱',
          style: TextStyle(
            color: textTheme.bodyLarge?.color ??
                   (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: controller.draft.name,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '請輸入類別名稱';
            }
            return null;
          },
          onChanged: (value) {
            controller.draft.name = value.trim();
          },
          decoration: const InputDecoration(
            hintText: '輸入類別名稱',
          ),
          style: TextStyle(
            fontSize: 16,
            color: textTheme.bodyLarge?.color ??
                   (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
          ),
        ),
      ],
    );
  }

  /// 构建颜色选择区域
  Widget _buildColorSection(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '選擇顏色',
        style: TextStyle(
          color: textTheme.bodyLarge?.color ??
                 (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      );
      yield const SizedBox(height: 12);
      yield Obx(() => _buildColorGrid(context));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  Widget _buildColorGrid(BuildContext context) {
    final colors = CategoryColors.availableColors;
    final draft = controller.draft;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 50, // 每個顏色圓圈最大寬度
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: colors.length,
      itemBuilder: (context, index) {
        final color = colors[index];
        final isSelected = draft.getColor() == color;

        return GestureDetector(
          onTap: () {
            draft.setColor(color);
            controller.refreshDraft();
          },
          child: Container(
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected
                    ? (isDark ? ErpColors.darkTextPrimary : Colors.white)
                    : Colors.transparent,
                width: 3,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: color.withOpacity(0.5),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
          ),
        );
      },
    );
  }

  /// 构建图标选择区域
  Widget _buildIconSection(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDark = theme.brightness == Brightness.dark;

    Iterable<Widget> getChildren() sync* {
      yield Text(
        '選擇圖示',
        style: TextStyle(
          color: textTheme.bodyLarge?.color ??
                 (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      );
      yield const SizedBox(height: 12);
      yield Obx(() => _buildIconGrid(context));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  Widget _buildIconGrid(BuildContext context) {
    final icons = CategoryIcons.availableIcons;
    final draft = controller.draft;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 50, // 每個顏色圓圈最大寬度
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: icons.length,
      itemBuilder: (context, index) {
        final icon = icons.elementAt(index);
        final isSelected = draft.getIcon() == icon;

        return GestureDetector(
          onTap: () {
            draft.setIcon(icon);
            controller.refreshDraft();
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? ErpColors.primary.withOpacity(0.1)
                  : (isDark ? ErpColors.darkCardBackground : Colors.white),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? ErpColors.primary
                    : (isDark ? ErpColors.darkBorder : ErpColors.border),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Icon(
              icon,
              color: isSelected
                  ? ErpColors.primary
                  : (isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary),
              size: 24,
            ),
          ),
        );
      },
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkCardBackground : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDark ? ErpColors.darkBorder : ErpColors.border,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 取消按钮
            Expanded(
              child: OutlinedButton(
                onPressed: () => Get.back(),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  side: BorderSide(
                    color: isDark ? ErpColors.darkBorder : ErpColors.border,
                  ),
                ),
                child: Text(
                  '取消',
                  style: TextStyle(
                    color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // 保存按钮
            Expanded(
              child: ElevatedButton(
                // onPressed: _saving,
                onPressed: controller.saveCategory,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ErpColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: controller.saveButtonState.obx(
                  (state) {
                    return Text(
                      'save'.tr,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    );
                  },
                  onLoading: const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  onError: (error) => Text(
                    'Error: $error',
                    style: const TextStyle(color: ErpColors.error),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
