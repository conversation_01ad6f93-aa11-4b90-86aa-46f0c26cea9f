import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../../models/erp_category.dart';
import '../../../repositories/category_repository.dart';

class CategoryDetailController extends GetxController with StateMixin<String> {
  final _disposable = Completer<void>();

  /// 分类仓库
  final CategoryRepository categoryRepository;
  Talker get talker => categoryRepository.talker;

  final formKey = GlobalKey<FormState>();

  // 当前编辑的分类（如果是编辑模式）
  final _draft = ErpCategory().obs;
  ErpCategory get draft => _draft.value;

  final _id = 0.obs;
  int get id => _id.value;

  final saveButtonState = ''.reactive;

  CategoryDetailController({
    required this.categoryRepository,
  });

  @override
  void onInit() {
    super.onInit();
    saveButtonState.change('', status: RxStatus.success());
  }

  @override
  void onReady() {
    super.onReady();
    final value = Get.parameters['id'] ?? '0';
    _id.value = int.tryParse(value) ?? 0;
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change(null, status: RxStatus.loading());
      if (id > 0) {
        // 编辑模式，加载现有分类
        final category = await categoryRepository.getByIdAsync(id);
        if (category != null) {
          _draft.value = category;
        }
      }
      change('', status: RxStatus.success());
    } catch (e, s) {
      talker.error('加载分类失败: $e', e, s);
      change(null, status: RxStatus.error('加载分类失败: $e'));
    }
  }

  /// 保存分类
  Future<void> saveCategory() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    saveButtonState.change('', status: RxStatus.loading());

    try {
      final ret = await categoryRepository.saveAsync(draft);
      Get.back(result: ret); // 返回上一页并传递成功结果
    } catch (e, s) {
      talker.error('保存分类失败: $e', e, s);
      saveButtonState.change(null, status: RxStatus.error('保存分类失败: $e'));
      return;
    } finally {
      saveButtonState.change('', status: RxStatus.success());
    }
  }

  void refreshDraft() {
    _draft.refresh();
  }
}
