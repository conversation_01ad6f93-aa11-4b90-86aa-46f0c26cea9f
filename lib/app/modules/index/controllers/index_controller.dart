import 'package:get/get.dart';

// Enum to represent the different tabs in the bottom navigation bar
enum TabIndex {
  home, // 0 - 主畫面
  orders, // 1 - 交易紀錄
  analysis, // 2 - 報表分析
  categories, // 3 - 類別管理
  settings // 4 - 設定
}

class IndexController extends GetxController {
  // Observable variable to track the current tab index using enum
  final _currentTab = TabIndex.home.obs;
  TabIndex get currentTab => _currentTab.value;

  void changeTab(TabIndex tab) {
    _currentTab.value = tab;
  }
}
