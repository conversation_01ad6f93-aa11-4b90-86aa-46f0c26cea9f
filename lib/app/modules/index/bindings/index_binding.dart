import 'package:get/get.dart';

import '../controllers/index_controller.dart';
import '../../home/<USER>/home_binding.dart';
import '../../orders/bindings/orders_binding.dart';
import '../../analysis/bindings/analysis_binding.dart';
import '../../categories/bindings/categories_binding.dart';
import '../../settings/bindings/settings_binding.dart';

class IndexBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<IndexController>(
      () => IndexController(),
    );

    // Initialize all sub-page bindings
    HomeBinding().dependencies();
    OrdersBinding().dependencies();
    AnalysisBinding().dependencies();
    CategoriesBinding().dependencies();
    SettingsBinding().dependencies();
  }
}
