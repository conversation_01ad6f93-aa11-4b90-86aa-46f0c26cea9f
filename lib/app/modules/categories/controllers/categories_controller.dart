import 'dart:async';

import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:talker_flutter/talker_flutter.dart';
import '../../../models/erp_category.dart';
import '../../../repositories/category_repository.dart';
import '../../../providers/box_provider.dart';

class CategoriesController extends GetxController with StateMixin<String> {
  final _disposable = Completer<void>();
  StreamSubscription? _categoriesSubscription;

  /// 分类仓库
  final CategoryRepository categoryRepository;
  BoxProvider get boxProvider => categoryRepository.boxProvider;
  Talker get talker => categoryRepository.talker;

  // 直接使用響應式變數存儲分類列表
  final _categories = <ErpCategory>[].obs;
  Iterable<ErpCategory> get categories => _categories;

  CategoriesController({
    required this.categoryRepository,
  });

  @override
  void onInit() {
    super.onInit();
    _startListening();
  }

  /// 開始監聽分類變更
  void _startListening() {
    _categoriesSubscription = categoryRepository
        .watchActive() // 只監聽未刪除的分類
        .takeUntil(_disposable.future)
        .listen(
          _onCategoriesChanged,
          onError: _onError,
        );
  }

  @override
  void onReady() {
    super.onReady();
    // onRefresh();
  }

  @override
  void onClose() {
    _categoriesSubscription?.cancel();
    _disposable.complete();
    super.onClose();
  }

  /// 處理分類變更事件
  void _onCategoriesChanged(Iterable<ErpCategory> newCategories) {
    try {
      _categories.assignAll(newCategories);

      if (newCategories.isEmpty) {
        change('', status: RxStatus.empty());
      } else {
        change('', status: RxStatus.success());
      }
    } catch (e, s) {
      talker.error('Failed to update categories: $e', e, s);
      _onError(e, s);
    }
  }

  /// 處理錯誤
  void _onError(Object error, [StackTrace? stackTrace]) {
    talker.error('Categories stream error: $error', error, stackTrace);
    change(null, status: RxStatus.error('載入分類失敗: $error'));
  }

  Future<void> onRefresh() async {
    try {
      change(null, status: RxStatus.loading());
      final categoryList = await categoryRepository.getAllAsync();
      _onCategoriesChanged(categoryList);
    } catch (e, s) {
      talker.error('Failed to load categories: $e', e, s);
      _onError(e, s);
    }
  }

  /// 搜尋分類
  void searchCategories(String query) {
    if (query.isEmpty) {
      _startListening(); // 重新開始正常監聽
      return;
    }

    _categoriesSubscription?.cancel();
    _categoriesSubscription = categoryRepository
        .watchByName(query)
        .takeUntil(_disposable.future)
        .listen(_onCategoriesChanged, onError: _onError);
  }

  /// 清除搜尋，回到正常狀態
  void clearSearch() {
    _startListening();
  }
}
