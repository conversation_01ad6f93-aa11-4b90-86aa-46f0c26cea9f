// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'app/models/erp_category.dart';
import 'app/models/erp_order.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 763025461432411501),
      name: '<PERSON>rp<PERSON>ate<PERSON><PERSON>',
      lastPropertyId: const obx_int.IdUid(8, 2808345654564820482),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 4392499479859630182),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 1766588938278149830),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 2769211468013534246),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 7375571393218057575),
            name: 'deletedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 198082410512833245),
            name: 'icon',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 7648433198262046330),
            name: 'name',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 826140357404482335),
            name: 'color',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 2808345654564820482),
            name: 'objectId',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[
        obx_int.ModelBacklink(
            name: 'children', srcEntity: 'ErpOrder', srcField: '')
      ]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(2, 48301052204748450),
      name: 'ErpOrder',
      lastPropertyId: const obx_int.IdUid(14, 7968351303227992232),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 6567497645279587144),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 7130241103431670466),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 6015555158435680228),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 5113910667507697869),
            name: 'deletedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 8657868146783911146),
            name: 'triggerAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 994060014392918221),
            name: 'type',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 4353773420859790641),
            name: 'amount',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 2517009719421306544),
            name: 'note',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 8676311522858340703),
            name: 'latitude',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 3057059151297440528),
            name: 'longitude',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 3092859197170109569),
            name: 'parentTable',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 7425091613525405949),
            name: 'parentObjectId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 6095793754874703228),
            name: 'objectId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(14, 7968351303227992232),
            name: 'parentId',
            type: 11,
            flags: 520,
            indexId: const obx_int.IdUid(1, 43690569976359661),
            relationTarget: 'ErpCategory')
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(2, 48301052204748450),
      lastIndexId: const obx_int.IdUid(1, 43690569976359661),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    ErpCategory: obx_int.EntityDefinition<ErpCategory>(
        model: _entities[0],
        toOneRelations: (ErpCategory object) => [],
        toManyRelations: (ErpCategory object) => {
              obx_int.RelInfo<ErpOrder>.toOneBacklink(
                      14, object.id!, (ErpOrder srcObject) => srcObject.parent):
                  object.children
            },
        getId: (ErpCategory object) => object.id,
        setId: (ErpCategory object, int id) {
          object.id = id;
        },
        objectToFB: (ErpCategory object, fb.Builder fbb) {
          final iconOffset =
              object.icon == null ? null : fbb.writeString(object.icon!);
          final nameOffset =
              object.name == null ? null : fbb.writeString(object.name!);
          final colorOffset =
              object.color == null ? null : fbb.writeString(object.color!);
          final objectIdOffset = object.objectId == null
              ? null
              : fbb.writeString(object.objectId!);
          fbb.startTable(9);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.deletedAt?.millisecondsSinceEpoch);
          fbb.addOffset(4, iconOffset);
          fbb.addOffset(5, nameOffset);
          fbb.addOffset(6, colorOffset);
          fbb.addOffset(7, objectIdOffset);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final deletedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 10);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final deletedAtParam = deletedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(deletedAtValue);
          final iconParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 12);
          final nameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 14);
          final objectIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final colorParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 16);
          final object = ErpCategory(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              deletedAt: deletedAtParam,
              icon: iconParam,
              name: nameParam,
              objectId: objectIdParam,
              color: colorParam);
          obx_int.InternalToManyAccess.setRelInfo<ErpCategory>(
              object.children,
              store,
              obx_int.RelInfo<ErpOrder>.toOneBacklink(
                  14, object.id!, (ErpOrder srcObject) => srcObject.parent));
          return object;
        }),
    ErpOrder: obx_int.EntityDefinition<ErpOrder>(
        model: _entities[1],
        toOneRelations: (ErpOrder object) => [object.parent],
        toManyRelations: (ErpOrder object) => {},
        getId: (ErpOrder object) => object.id,
        setId: (ErpOrder object, int id) {
          object.id = id;
        },
        objectToFB: (ErpOrder object, fb.Builder fbb) {
          final noteOffset =
              object.note == null ? null : fbb.writeString(object.note!);
          final parentTableOffset = object.parentTable == null
              ? null
              : fbb.writeString(object.parentTable!);
          final parentObjectIdOffset = object.parentObjectId == null
              ? null
              : fbb.writeString(object.parentObjectId!);
          final objectIdOffset = object.objectId == null
              ? null
              : fbb.writeString(object.objectId!);
          fbb.startTable(15);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.deletedAt?.millisecondsSinceEpoch);
          fbb.addInt64(4, object.triggerAt?.millisecondsSinceEpoch);
          fbb.addInt64(5, object.type);
          fbb.addFloat64(6, object.amount);
          fbb.addOffset(7, noteOffset);
          fbb.addFloat64(8, object.latitude);
          fbb.addFloat64(9, object.longitude);
          fbb.addOffset(10, parentTableOffset);
          fbb.addOffset(11, parentObjectIdOffset);
          fbb.addOffset(12, objectIdOffset);
          fbb.addInt64(13, object.parent.targetId);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final deletedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 10);
          final triggerAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 12);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final deletedAtParam = deletedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(deletedAtValue);
          final triggerAtParam = triggerAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(triggerAtValue);
          final latitudeParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 20);
          final longitudeParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 22);
          final typeParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 14);
          final amountParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 16);
          final noteParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final parentTableParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 24);
          final parentObjectIdParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 26);
          final objectIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 28);
          final object = ErpOrder(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              deletedAt: deletedAtParam,
              triggerAt: triggerAtParam,
              latitude: latitudeParam,
              longitude: longitudeParam,
              type: typeParam,
              amount: amountParam,
              note: noteParam,
              parentTable: parentTableParam,
              parentObjectId: parentObjectIdParam,
              objectId: objectIdParam);
          object.parent.targetId =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 30, 0);
          object.parent.attach(store);
          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [ErpCategory] entity fields to define ObjectBox queries.
class ErpCategory_ {
  /// See [ErpCategory.id].
  static final id =
      obx.QueryIntegerProperty<ErpCategory>(_entities[0].properties[0]);

  /// See [ErpCategory.createdAt].
  static final createdAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[1]);

  /// See [ErpCategory.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[2]);

  /// See [ErpCategory.deletedAt].
  static final deletedAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[3]);

  /// See [ErpCategory.icon].
  static final icon =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[4]);

  /// See [ErpCategory.name].
  static final name =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[5]);

  /// See [ErpCategory.color].
  static final color =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[6]);

  /// See [ErpCategory.objectId].
  static final objectId =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[7]);

  /// see [ErpCategory.children]
  static final children =
      obx.QueryBacklinkToMany<ErpOrder, ErpCategory>(ErpOrder_.parent);
}

/// [ErpOrder] entity fields to define ObjectBox queries.
class ErpOrder_ {
  /// See [ErpOrder.id].
  static final id =
      obx.QueryIntegerProperty<ErpOrder>(_entities[1].properties[0]);

  /// See [ErpOrder.createdAt].
  static final createdAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[1]);

  /// See [ErpOrder.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[2]);

  /// See [ErpOrder.deletedAt].
  static final deletedAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[3]);

  /// See [ErpOrder.triggerAt].
  static final triggerAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[4]);

  /// See [ErpOrder.type].
  static final type =
      obx.QueryIntegerProperty<ErpOrder>(_entities[1].properties[5]);

  /// See [ErpOrder.amount].
  static final amount =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[6]);

  /// See [ErpOrder.note].
  static final note =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[7]);

  /// See [ErpOrder.latitude].
  static final latitude =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[8]);

  /// See [ErpOrder.longitude].
  static final longitude =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[9]);

  /// See [ErpOrder.parentTable].
  static final parentTable =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[10]);

  /// See [ErpOrder.parentObjectId].
  static final parentObjectId =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[11]);

  /// See [ErpOrder.objectId].
  static final objectId =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[12]);

  /// See [ErpOrder.parent].
  static final parent = obx.QueryRelationToOne<ErpOrder, ErpCategory>(
      _entities[1].properties[13]);
}
