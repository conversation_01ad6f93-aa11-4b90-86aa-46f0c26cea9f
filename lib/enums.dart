enum Boxes {
  settings,
  categories,
  orders,
}

enum Button {
  cancel,
  confirm,
}

extension ButtonX on Button {
  String get display {
    switch (this) {
      case Button.confirm:
        return '確認';
      case Button.cancel:
        return '取消';
    }
  }

  bool get value {
    switch (this) {
      case Button.confirm:
        return true;
      case Button.cancel:
        return false;
    }
  }
}

enum OrderType {
  none,
  expense,
  income,
}

extension OrderTypeX on OrderType {
  String get display {
    switch (this) {
      case OrderType.none:
        return '未知';
      case OrderType.expense:
        return '支出';
      case OrderType.income:
        return '收入';
    }
  }
}

enum ActionType {
  none,
  add,
  edit,
  delete,
  read,
  put,
}

extension ActionX on ActionType {
  String get display {
    switch (this) {
      case ActionType.add:
        return '新增';
      case ActionType.edit:
        return '編輯';
      case ActionType.delete:
        return '刪除';
      case ActionType.read:
        return '查看';
      case ActionType.put:
        return '放置';
      case ActionType.none:
        return '未知';
    }
  }

  bool get isAdd => this == ActionType.add;
  bool get isEdit => this == ActionType.edit;
  bool get isDelete => this == ActionType.delete;
}
