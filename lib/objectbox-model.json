{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:763025461432411501", "lastPropertyId": "8:2808345654564820482", "name": "ErpCategory", "properties": [{"id": "1:4392499479859630182", "name": "id", "type": 6, "flags": 1}, {"id": "2:1766588938278149830", "name": "createdAt", "type": 10}, {"id": "3:2769211468013534246", "name": "updatedAt", "type": 10}, {"id": "4:7375571393218057575", "name": "deletedAt", "type": 10}, {"id": "5:198082410512833245", "name": "icon", "type": 9}, {"id": "6:7648433198262046330", "name": "name", "type": 9}, {"id": "7:826140357404482335", "name": "color", "type": 9}, {"id": "8:2808345654564820482", "name": "objectId", "type": 9}], "relations": []}, {"id": "2:48301052204748450", "lastPropertyId": "14:7968351303227992232", "name": "ErpOrder", "properties": [{"id": "1:6567497645279587144", "name": "id", "type": 6, "flags": 1}, {"id": "2:7130241103431670466", "name": "createdAt", "type": 10}, {"id": "3:6015555158435680228", "name": "updatedAt", "type": 10}, {"id": "4:5113910667507697869", "name": "deletedAt", "type": 10}, {"id": "5:8657868146783911146", "name": "triggerAt", "type": 10}, {"id": "6:994060014392918221", "name": "type", "type": 6}, {"id": "7:4353773420859790641", "name": "amount", "type": 8}, {"id": "8:2517009719421306544", "name": "note", "type": 9}, {"id": "9:8676311522858340703", "name": "latitude", "type": 8}, {"id": "10:3057059151297440528", "name": "longitude", "type": 8}, {"id": "11:3092859197170109569", "name": "parentTable", "type": 9}, {"id": "12:7425091613525405949", "name": "parentObjectId", "type": 9}, {"id": "13:6095793754874703228", "name": "objectId", "type": 9}, {"id": "14:7968351303227992232", "name": "parentId", "type": 11, "flags": 520, "indexId": "1:43690569976359661", "relationTarget": "ErpCategory"}], "relations": []}], "lastEntityId": "2:48301052204748450", "lastIndexId": "1:43690569976359661", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}